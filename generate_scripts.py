#!/usr/bin/env python3
"""
Script Database Generator for CyberWolf

This script generates a large database of attack scripts, payloads, and security testing
vectors for use with the CyberWolf security scanner. It dynamically creates variations
of common attack patterns to improve detection capabilities.

Usage:
    python generate_scripts.py [--count NUMBER] [--output FILE]

Options:
    --count NUMBER    Number of scripts to generate (default: 100000)
    --output FILE     Output file to write scripts to (default: cyberwolf/data/dynamic_payloads.txt)

Author: <PERSON><PERSON> (Cybersecurity Researcher)
"""

import os
import sys
import argparse
import random
import itertools
import string
from datetime import datetime
from pathlib import Path

# Make sure we can import from cyberwolf package
sys.path.insert(0, os.path.abspath('.'))

# Import the payload generator
from cyberwolf.utils.payload_generator import generate_dynamic_payloads, save_dynamic_payloads

def main():
    """Main entry point for the script generator"""
    parser = argparse.ArgumentParser(description='Generate security testing scripts for CyberWolf.')
    parser.add_argument('--count', type=int, default=100000,
                      help='Number of scripts to generate (default: 100000)')
    parser.add_argument('--output', type=str, default='cyberwolf/data/dynamic_payloads.txt',
                      help='Output file to write scripts to')
    
    args = parser.parse_args()
    
    print("""
 ██████╗██╗   ██╗██████╗ ███████╗██████╗ ██╗    ██╗ ██████╗ ██╗     ███████╗
██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝██╔══██╗██║    ██║██╔═══██╗██║     ██╔════╝
██║      ╚████╔╝ ██████╔╝█████╗  ██████╔╝██║ █╗ ██║██║   ██║██║     █████╗  
██║       ╚██╔╝  ██╔══██╗██╔══╝  ██╔══██╗██║███╗██║██║   ██║██║     ██╔══╝  
╚██████╗   ██║   ██████╔╝███████╗██║  ██║╚███╔███╔╝╚██████╔╝███████╗██║     
 ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝ ╚══╝╚══╝  ╚═════╝ ╚══════╝╚═╝     
                                                       SCRIPT GENERATOR v1.0
""")
    
    print(f"[+] Starting script generation: {args.count} scripts")
    print(f"[+] Output file: {args.output}")
    
    start_time = datetime.now()
    print(f"[+] Generation started at: {start_time}")
    
    try:
        # Generate and save the payloads
        count = save_dynamic_payloads(output_file=args.output.split('/')[-1], max_payloads=args.count)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"[+] Successfully generated {count} attack scripts")
        print(f"[+] Generation completed at: {end_time}")
        print(f"[+] Total time: {duration}")
        print(f"[+] Scripts saved to: {args.output}")
        
    except Exception as e:
        print(f"[!] Error generating scripts: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()