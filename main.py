"""
CyberWolf Web Interface

This provides a web interface for CyberWolf vulnerability scanner.
"""

from flask import Flask, render_template, request, jsonify
import os
import logging
from rich.console import Console
import time
import threading
import json

# Initialize Flask app
app = Flask(__name__)
app.config["SECRET_KEY"] = os.environ.get("SESSION_SECRET", "cyberwolf-secure-key-2024")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Setup console for rich output
console = Console()

@app.route('/')
def index():
    """Render the main scanner interface"""
    return render_template('index.html')

@app.route('/scan', methods=['POST'])
def scan():
    """Run a scan on the target URL"""
    target_url = request.form.get('target_url')
    
    if not target_url:
        return jsonify({"error": "No target URL provided"}), 400
    
    # Validate URL
    if not target_url.startswith(('http://', 'https://')):
        target_url = 'https://' + target_url
    
    # Setup scan options from form data
    scan_options = {
        "scan_sql": request.form.get('scan_sql') == 'on',
        "scan_xss": request.form.get('scan_xss') == 'on',
        "scan_csrf": request.form.get('scan_csrf') == 'on',
        "scan_dirs": request.form.get('scan_dirs') == 'on',
        "tamil_brak": request.form.get('tamil_brak') == 'on',
        "scan_depth": int(request.form.get('scan_depth', 2)),
        "threads": int(request.form.get('threads', 5)),
        "timeout": int(request.form.get('timeout', 30)),
        "rate_limit": int(request.form.get('rate_limit', 10))
    }
    
    # Start scan in background thread
    scan_id = str(int(time.time()))
    threading.Thread(target=run_scan, args=(scan_id, target_url, scan_options)).start()
    
    return jsonify({
        "status": "started", 
        "scan_id": scan_id,
        "message": f"Scan started on {target_url}"
    })

@app.route('/scan-status/<scan_id>')
def scan_status(scan_id):
    """Get the status of a running scan"""
    # In a real implementation, we would check a database or file
    # For now, just return a mock response
    return jsonify({
        "status": "in_progress",
        "progress": 75,
        "current_phase": "Testing XSS vulnerabilities"
    })

@app.route('/scan-results/<scan_id>')
def scan_results(scan_id):
    """Get the results of a completed scan"""
    # In a real implementation, we would load results from a database or file
    result_file = f"results/{scan_id}.json"
    
    if os.path.exists(result_file):
        with open(result_file, 'r') as f:
            results = json.load(f)
        return jsonify(results)
    
    return jsonify({"error": "Results not found"}), 404

def run_scan(scan_id, target_url, options):
    """Run the scan in a background thread"""
    from rich.progress import Progress
    from cyberwolf.scanner import Scanner
    
    # Ensure results directory exists
    os.makedirs("results", exist_ok=True)
    
    try:
        # Create a progress object
        with Progress() as progress:
            # Initialize scanner
            scanner = Scanner(target_url, options, progress)
            
            # Run the scan
            results = scanner.run()
            
            # Save results to file
            with open(f"results/{scan_id}.json", 'w') as f:
                json.dump(results, f, indent=2)
            
            logger.info(f"Scan {scan_id} completed successfully.")
    except Exception as e:
        logger.error(f"Error in scan {scan_id}: {str(e)}")
        
        # Save error information
        with open(f"results/{scan_id}.json", 'w') as f:
            json.dump({"error": str(e)}, f)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)