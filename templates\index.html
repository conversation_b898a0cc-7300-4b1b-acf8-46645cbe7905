<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CyberWolf - Website Vulnerability Scanner</title>
    <link rel="stylesheet" href="https://cdn.replit.com/agent/bootstrap-agent-dark-theme.min.css">
    <style>
        .logo {
            max-width: 120px;
        }
        .vulnerability-card {
            margin-bottom: 15px;
            border-left: 5px solid var(--bs-info);
        }
        .vulnerability-card.severity-high {
            border-left-color: var(--bs-danger);
        }
        .vulnerability-card.severity-medium {
            border-left-color: var(--bs-warning);
        }
        .vulnerability-card.severity-low {
            border-left-color: var(--bs-info);
        }
        .card-header {
            font-weight: bold;
        }
        pre {
            background-color: var(--bs-dark);
            padding: 10px;
            border-radius: 4px;
        }
        .scan-options {
            margin-top: 20px;
        }
        .progress {
            height: 25px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row mb-5">
            <div class="col-md-10">
                <h1>CyberWolf</h1>
                <h3 class="text-secondary">Advanced Website Vulnerability Scanner</h3>
                <p class="lead">
                    Developed by S.Tamilselvan (Cybersecurity Researcher)
                </p>
            </div>
            <div class="col-md-2 text-end">
                <svg class="logo" xmlns="http://www.w3.org/2000/svg" width="120" height="120" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                    <path d="M2 17l10 5 10-5"></path>
                    <path d="M2 12l10 5 10-5"></path>
                </svg>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    <div class="card-header">
                        New Scan
                    </div>
                    <div class="card-body">
                        <form id="scanForm">
                            <div class="mb-3">
                                <label for="targetUrl" class="form-label">Target URL</label>
                                <input type="text" class="form-control" id="targetUrl" name="target_url" placeholder="https://example.com" required>
                                <div class="form-text">Enter the full URL of the website to scan.</div>
                            </div>

                            <div class="row scan-options">
                                <div class="col-md-6">
                                    <h5>Vulnerability Types</h5>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" value="" id="scanSql" name="scan_sql" checked>
                                        <label class="form-check-label" for="scanSql">
                                            SQL Injection
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" value="" id="scanXss" name="scan_xss" checked>
                                        <label class="form-check-label" for="scanXss">
                                            Cross-Site Scripting (XSS)
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" value="" id="scanCsrf" name="scan_csrf" checked>
                                        <label class="form-check-label" for="scanCsrf">
                                            Cross-Site Request Forgery (CSRF)
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" value="" id="scanDirs" name="scan_dirs" checked>
                                        <label class="form-check-label" for="scanDirs">
                                            Directory Scanning
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" value="" id="tamilBrak" name="tamil_brak">
                                        <label class="form-check-label" for="tamilBrak">
                                            Tamil-Brak Advanced Attack Mode
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h5>Scan Settings</h5>
                                    <div class="mb-3">
                                        <label for="scanDepth" class="form-label">Scan Depth</label>
                                        <select class="form-select" id="scanDepth" name="scan_depth">
                                            <option value="1">Light (1)</option>
                                            <option value="2" selected>Medium (2)</option>
                                            <option value="3">Deep (3)</option>
                                            <option value="4">Thorough (4)</option>
                                            <option value="5">Comprehensive (5)</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="threads" class="form-label">Threads</label>
                                        <input type="range" class="form-range" min="1" max="10" value="5" id="threads" name="threads">
                                        <div class="d-flex justify-content-between">
                                            <span>1</span>
                                            <span>5</span>
                                            <span>10</span>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="timeout" class="form-label">Request Timeout (seconds)</label>
                                        <input type="number" class="form-control" id="timeout" name="timeout" value="30" min="5" max="120">
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <i class="bi bi-info-circle"></i> The Tamil-Brak attack mode performs a comprehensive scan using multiple advanced techniques developed by S.Tamilselvan, including test vectors from the 100,000+ attack database.
                            </div>

                            <button type="submit" class="btn btn-primary mt-3" id="scanButton">
                                <i class="bi bi-search"></i> Start Scan
                            </button>
                        </form>
                    </div>
                </div>

                <div id="scanningProgress" style="display: none;">
                    <div class="card mb-4">
                        <div class="card-header">
                            Scan Progress
                        </div>
                        <div class="card-body">
                            <h5 id="scanTarget"></h5>
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" id="progressBar">0%</div>
                            </div>
                            <p id="currentOperation">Initializing scan...</p>
                        </div>
                    </div>
                </div>

                <div id="scanResults" style="display: none;">
                    <div class="card mb-4">
                        <div class="card-header">
                            Scan Results
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <h5>Summary</h5>
                                    <table class="table">
                                        <tbody>
                                            <tr>
                                                <td>Target</td>
                                                <td id="resultTarget">example.com</td>
                                            </tr>
                                            <tr>
                                                <td>Scan Time</td>
                                                <td id="scanTime">2m 34s</td>
                                            </tr>
                                            <tr>
                                                <td>Total Vulnerabilities</td>
                                                <td id="totalVulnerabilities">0</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h5>Risk Overview</h5>
                                    <div class="d-flex mb-2">
                                        <div class="me-2 text-danger">High:</div>
                                        <div id="highRisk">0</div>
                                    </div>
                                    <div class="d-flex mb-2">
                                        <div class="me-2 text-warning">Medium:</div>
                                        <div id="mediumRisk">0</div>
                                    </div>
                                    <div class="d-flex mb-2">
                                        <div class="me-2 text-info">Low:</div>
                                        <div id="lowRisk">0</div>
                                    </div>
                                </div>
                            </div>

                            <ul class="nav nav-tabs" id="resultTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="sql-tab" data-bs-toggle="tab" data-bs-target="#sql" type="button" role="tab">SQL Injection</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="xss-tab" data-bs-toggle="tab" data-bs-target="#xss" type="button" role="tab">XSS</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="csrf-tab" data-bs-toggle="tab" data-bs-target="#csrf" type="button" role="tab">CSRF</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="dirs-tab" data-bs-toggle="tab" data-bs-target="#dirs" type="button" role="tab">Directories</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="tamilbrak-tab" data-bs-toggle="tab" data-bs-target="#tamilbrak" type="button" role="tab">Tamil-Brak</button>
                                </li>
                            </ul>
                            <div class="tab-content p-3" id="resultTabContent">
                                <div class="tab-pane fade show active" id="sql" role="tabpanel" aria-labelledby="sql-tab">
                                    <div id="sqlResults">
                                        <div class="alert alert-info">No SQL Injection vulnerabilities found.</div>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="xss" role="tabpanel" aria-labelledby="xss-tab">
                                    <div id="xssResults">
                                        <div class="alert alert-info">No XSS vulnerabilities found.</div>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="csrf" role="tabpanel" aria-labelledby="csrf-tab">
                                    <div id="csrfResults">
                                        <div class="alert alert-info">No CSRF vulnerabilities found.</div>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="dirs" role="tabpanel" aria-labelledby="dirs-tab">
                                    <div id="dirsResults">
                                        <div class="alert alert-info">No directory vulnerabilities found.</div>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="tamilbrak" role="tabpanel" aria-labelledby="tamilbrak-tab">
                                    <div id="tamilbrakResults">
                                        <div class="alert alert-info">Tamil-Brak attack mode not enabled for this scan.</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <footer class="mt-5 mb-3 text-center">
            <p>CyberWolf - Advanced Website Vulnerability Scanner<br>
               Developed by S.Tamilselvan (Cybersecurity Researcher)</p>
        </footer>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const scanForm = document.getElementById('scanForm');
            const scanningProgress = document.getElementById('scanningProgress');
            const scanResults = document.getElementById('scanResults');
            const progressBar = document.getElementById('progressBar');
            const currentOperation = document.getElementById('currentOperation');
            const scanTarget = document.getElementById('scanTarget');
            
            // Mock scan results for demonstration
            const mockScanData = {
                target: "https://example.com",
                scan_time: 154.32,
                vulnerabilities: {
                    sql_injection: [
                        {
                            url: "https://example.com/search?q=test",
                            severity: "high",
                            description: "Blind SQL Injection in search parameter",
                            evidence: "Parameter 'q' is vulnerable to SQL injection",
                            remediation: "Use parameterized queries or prepared statements"
                        }
                    ],
                    xss: [
                        {
                            url: "https://example.com/profile?name=test",
                            severity: "medium",
                            description: "Reflected XSS in name parameter",
                            evidence: "Parameter 'name' is reflected without proper encoding",
                            remediation: "Implement proper output encoding for the 'name' parameter"
                        }
                    ],
                    csrf: [],
                    directories: [
                        {
                            url: "https://example.com/admin/",
                            severity: "medium",
                            description: "Admin directory accessible",
                            evidence: "Directory /admin/ is accessible without authentication",
                            remediation: "Secure the admin directory with proper authentication"
                        },
                        {
                            url: "https://example.com/backup/",
                            severity: "low",
                            description: "Backup directory found",
                            evidence: "Directory /backup/ is publicly accessible",
                            remediation: "Remove or restrict access to backup directories"
                        }
                    ],
                    tamil_brak: [
                        {
                            url: "https://example.com/config.php",
                            severity: "critical",
                            description: "Source code disclosure",
                            evidence: "PHP source code is visible due to misconfiguration",
                            remediation: "Configure server to prevent source code disclosure"
                        }
                    ]
                },
                summary: {
                    total_vulnerabilities: 5,
                    high_risk: 1,
                    medium_risk: 2,
                    low_risk: 1,
                    critical_risk: 1
                }
            };
            
            scanForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Hide form, show progress
                scanForm.parentElement.parentElement.style.display = 'none';
                scanningProgress.style.display = 'block';
                
                const formData = new FormData(scanForm);
                const targetUrl = formData.get('target_url');
                scanTarget.textContent = `Scanning: ${targetUrl}`;
                
                // Simulate scan progress
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 5;
                    if (progress <= 100) {
                        progressBar.style.width = `${progress}%`;
                        progressBar.textContent = `${progress}%`;
                        
                        if (progress < 20) {
                            currentOperation.textContent = "Testing connection to target...";
                        } else if (progress < 40) {
                            currentOperation.textContent = "Scanning for SQL Injection vulnerabilities...";
                        } else if (progress < 60) {
                            currentOperation.textContent = "Scanning for XSS vulnerabilities...";
                        } else if (progress < 80) {
                            currentOperation.textContent = "Scanning for directory vulnerabilities...";
                        } else {
                            currentOperation.textContent = "Finalizing results...";
                        }
                    } else {
                        clearInterval(interval);
                        displayResults(mockScanData);
                    }
                }, 200);
                
                // In a real implementation, we would make an AJAX call to the server
                // fetch('/scan', {
                //     method: 'POST',
                //     body: formData
                // })
                // .then(response => response.json())
                // .then(data => {
                //     // Start polling for scan status
                //     pollScanStatus(data.scan_id);
                // })
                // .catch(error => {
                //     console.error('Error:', error);
                // });
            });
            
            function displayResults(data) {
                // Hide progress, show results
                scanningProgress.style.display = 'none';
                scanResults.style.display = 'block';
                
                // Update summary information
                document.getElementById('resultTarget').textContent = data.target;
                
                const minutes = Math.floor(data.scan_time / 60);
                const seconds = Math.round(data.scan_time % 60);
                document.getElementById('scanTime').textContent = `${minutes}m ${seconds}s`;
                
                document.getElementById('totalVulnerabilities').textContent = data.summary.total_vulnerabilities;
                document.getElementById('highRisk').textContent = data.summary.high_risk;
                document.getElementById('mediumRisk').textContent = data.summary.medium_risk;
                document.getElementById('lowRisk').textContent = data.summary.low_risk;
                
                // Display vulnerability details
                displayVulnerabilityType('sqlResults', data.vulnerabilities.sql_injection);
                displayVulnerabilityType('xssResults', data.vulnerabilities.xss);
                displayVulnerabilityType('csrfResults', data.vulnerabilities.csrf);
                displayVulnerabilityType('dirsResults', data.vulnerabilities.directories);
                
                // Tamil-Brak results
                if (data.vulnerabilities.tamil_brak && data.vulnerabilities.tamil_brak.length > 0) {
                    displayVulnerabilityType('tamilbrakResults', data.vulnerabilities.tamil_brak);
                }
            }
            
            function displayVulnerabilityType(elementId, vulnerabilities) {
                const container = document.getElementById(elementId);
                
                if (!vulnerabilities || vulnerabilities.length === 0) {
                    container.innerHTML = '<div class="alert alert-info">No vulnerabilities found.</div>';
                    return;
                }
                
                container.innerHTML = '';
                
                vulnerabilities.forEach(vuln => {
                    const card = document.createElement('div');
                    card.className = `card vulnerability-card severity-${vuln.severity.toLowerCase()}`;
                    
                    card.innerHTML = `
                        <div class="card-header d-flex justify-content-between">
                            <span>${vuln.description}</span>
                            <span class="badge ${getBadgeClass(vuln.severity)}">${vuln.severity.toUpperCase()}</span>
                        </div>
                        <div class="card-body">
                            <p><strong>URL:</strong> ${vuln.url}</p>
                            <p><strong>Evidence:</strong> ${vuln.evidence}</p>
                            <p><strong>Remediation:</strong> ${vuln.remediation}</p>
                        </div>
                    `;
                    
                    container.appendChild(card);
                });
            }
            
            function getBadgeClass(severity) {
                switch (severity.toLowerCase()) {
                    case 'critical':
                    case 'high':
                        return 'bg-danger';
                    case 'medium':
                        return 'bg-warning';
                    case 'low':
                        return 'bg-info';
                    default:
                        return 'bg-secondary';
                }
            }
            
            // For a real implementation, we would poll the server for scan status
            function pollScanStatus(scanId) {
                const statusInterval = setInterval(() => {
                    fetch(`/scan-status/${scanId}`)
                        .then(response => response.json())
                        .then(data => {
                            progressBar.style.width = `${data.progress}%`;
                            progressBar.textContent = `${data.progress}%`;
                            currentOperation.textContent = data.current_phase;
                            
                            if (data.status === 'completed') {
                                clearInterval(statusInterval);
                                
                                // Fetch and display results
                                fetch(`/scan-results/${scanId}`)
                                    .then(response => response.json())
                                    .then(resultData => {
                                        displayResults(resultData);
                                    });
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            clearInterval(statusInterval);
                        });
                }, 1000);
            }
        });
    </script>
</body>
</html>