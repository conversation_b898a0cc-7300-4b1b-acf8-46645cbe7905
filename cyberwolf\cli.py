"""
Command Line Interface for CyberWolf
"""

import os
import sys
import time
import logging
import click
from urllib.parse import urlparse
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.panel import Panel
from rich.text import Text

from cyberwolf.scanner import Scanner
from cyberwolf.utils.validator import validate_url
from cyberwolf.report.console import generate_console_report
from cyberwolf.report.html import generate_html_report
from cyberwolf.report.pdf import generate_pdf_report

# Setup logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Setup rich console
console = Console()

BANNER = r"""
 ⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⣀⡀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀
⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠸⠁⠸⢳⡄⠀⠀⠀⠀⠀⠀⠀⠀
⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⢠⠃⠀⠀⢸⠸⠀⡠⣄⠀⠀⠀⠀⠀
⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⡠⠃⠀⠀⢠⣞⣀⡿⠀⠀⣧⠀⠀⠀⠀
⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⣀⣠⡖⠁⠀⠀⠀⢸⠈⢈⡇⠀⢀⡏⠀⠀⠀⠀
⠀⠀⠀⠀⠀⠀⠀⠀⠀⡴⠩⢠⡴⠀⠀⠀⠀⠀⠈⡶⠉⠀⠀⡸⠀⠀⠀⠀⠀
⠀⠀⠀⠀⠀⠀⠀⢀⠎⢠⣇⠏⠀⠀⠀⠀⠀⠀⠀⠁⠀⢀⠄⡇⠀⠀⠀⠀⠀
⠀⠀⠀⠀⠀⠀⢠⠏⠀⢸⣿⣴⠀⠀⠀⠀⠀⠀⣆⣀⢾⢟⠴⡇⠀⠀⠀⠀⠀
⠀⠀⠀⠀⠀⢀⣿⠀⠠⣄⠸⢹⣦⠀⠀⡄⠀⠀⢋⡟⠀⠀⠁⣇⠀⠀⠀⠀⠀
⠀⠀⠀⠀⢀⡾⠁⢠⠀⣿⠃⠘⢹⣦⢠⣼⠀⠀⠉⠀⠀⠀⠀⢸⡀⠀⠀⠀⠀
⠀⠀⢀⣴⠫⠤⣶⣿⢀⡏⠀⠀⠘⢸⡟⠋⠀⠀⠀⠀⠀⠀⠀⠀⢳⠀⠀⠀⠀
⠐⠿⢿⣿⣤⣴⣿⣣⢾⡄⠀⠀⠀⠀⠳⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⢣⠀⠀⠀
⠀⠀⠀⣨⣟⡍⠉⠚⠹⣇⡄⠀⠀⠀⠀⠀⠀⠀⠀⠈⢦⠀⠀⢀⡀⣾⡇⠀⠀
⠀⠀⢠⠟⣹⣧⠃⠀⠀⢿⢻⡀⢄⠀⠀⠀⠀⠐⣦⡀⣸⣆⠀⣾⣧⣯⢻⠀⠀
⠀⠀⠘⣰⣿⣿⡄⡆⠀⠀⠀⠳⣼⢦⡘⣄⠀⠀⡟⡷⠃⠘⢶⣿⡎⠻⣆⠀⠀
⠀⠀⠀⡟⡿⢿⡿⠀⠀⠀⠀⠀⠙⠀⠻⢯⢷⣼⠁⠁⠀⠀⠀⠙⢿⡄⡈⢆⠀
⠀⠀⠀⠀⡇⣿⡅⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠙⠦⠀⠀⠀⠀⠀⠀⡇⢹⢿⡀
⠀⠀⠀⠀⠁⠛⠓⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠼⠇⠁

 ██████╗██╗   ██╗██████╗ ███████╗██████╗ ██╗    ██╗ ██████╗ ██╗     ███████╗
██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝██╔══██╗██║    ██║██╔═══██╗██║     ██╔════╝
██║      ╚████╔╝ ██████╔╝█████╗  ██████╔╝██║ █╗ ██║██║   ██║██║     █████╗  
██║       ╚██╔╝  ██╔══██╗██╔══╝  ██╔══██╗██║███╗██║██║   ██║██║     ██╔══╝  
╚██████╗   ██║   ██████╔╝███████╗██║  ██║╚███╔███╔╝╚██████╔╝███████╗██║     
 ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝ ╚══╝╚══╝  ╚═════╝ ╚══════╝╚═╝     

 

    ███        ▄████████   ▄▄▄▄███▄▄▄▄    ▄█   ▄█          ▄████████    ▄████████  ▄█        ▄█    █▄     ▄████████ ███▄▄▄▄   
▀█████████▄   ███    ███ ▄██▀▀▀███▀▀▀██▄ ███  ███         ███    ███   ███    ███ ███       ███    ███   ███    ███ ███▀▀▀██▄ 
   ▀███▀▀██   ███    ███ ███   ███   ███ ███▌ ███         ███    █▀    ███    █▀  ███       ███    ███   ███    ███ ███   ███ 
    ███   ▀   ███    ███ ███   ███   ███ ███▌ ███         ███         ▄███▄▄▄     ███       ███    ███   ███    ███ ███   ███ 
    ███     ▀███████████ ███   ███   ███ ███▌ ███       ▀███████████ ▀▀███▀▀▀     ███       ███    ███ ▀███████████ ███   ███ 
    ███       ███    ███ ███   ███   ███ ███  ███                ███   ███    █▄  ███       ███    ███   ███    ███ ███   ███ 
    ███       ███    ███ ███   ███   ███ ███  ███▌    ▄    ▄█    ███   ███    ███ ███▌    ▄ ███    ███   ███    ███ ███   ███ 
   ▄████▀     ███    █▀   ▀█   ███   █▀  █▀   █████▄▄██  ▄████████▀    ██████████ █████▄▄██  ▀██████▀    ███    █▀   ▀█   █▀  
                                              ▀                                   ▀                                           
 
 
 
"""

DISCLAIMER = """
DISCLAIMER: This tool is intended for ethical security testing only.
Unauthorized scanning of websites without proper permission is illegal and unethical.
Always ensure you have authorization before scanning any website.
Use responsibly!
"""

@click.group()
@click.version_option(version="1.10.0")
def cli():
    """CyberWolf - A Website Vulnerability Scanner"""
    pass

@cli.command()
@click.argument('url')
@click.option('--output', '-o', type=click.Choice(['console', 'html', 'pdf']), default='console',
              help='Output format for the vulnerability report')
@click.option('--output-file', '-f', default=None, help='Output file name for HTML/PDF reports')
@click.option('--scan-depth', '-d', default=2, help='Depth of directory scanning (1-5)')
@click.option('--timeout', '-t', default=30, help='Request timeout in seconds')
@click.option('--threads', '-th', default=5, help='Number of threads to use for scanning')
@click.option('--rate-limit', '-r', default=10, help='Requests per second rate limit')
@click.option('--scan-sql/--no-scan-sql', default=True, help='Enable/disable SQL injection scanning')
@click.option('--scan-xss/--no-scan-xss', default=True, help='Enable/disable XSS scanning')
@click.option('--scan-csrf/--no-scan-csrf', default=True, help='Enable/disable CSRF scanning')
@click.option('--scan-dirs/--no-scan-dirs', default=True, help='Enable/disable directory scanning')
@click.option('--tamil-brak', is_flag=True, help='Enable the powerful Tamil-Brak attack mode for comprehensive site testing')
@click.option('--yes', '-y', is_flag=True, help='Skip confirmation prompts')
def scan(url, output, output_file, scan_depth, timeout, threads, rate_limit, 
         scan_sql, scan_xss, scan_csrf, scan_dirs, tamil_brak, yes):
    """Scan a target website for vulnerabilities"""
    # Display banner
    console.print(Panel(Text(BANNER, style="bold blue")))
    console.print(Panel(Text(DISCLAIMER, style="bold red")))
    
    # Validate URL
    if not validate_url(url):
        console.print(f"[bold red]Error:[/bold red] Invalid URL provided: {url}")
        console.print("Please provide a valid URL (e.g., https://example.com)")
        return 1
    
    # Get confirmation from user
    if not yes:
        console.print(f"[bold yellow]Target:[/bold yellow] {url}")
        console.print(f"[bold yellow]Scan Options:[/bold yellow]")
        console.print(f"  Scan Depth: {scan_depth}")
        console.print(f"  Threads: {threads}")
        console.print(f"  Rate Limit: {rate_limit} requests per second")
        console.print(f"  SQL Injection Scanning: {'Enabled' if scan_sql else 'Disabled'}")
        console.print(f"  XSS Scanning: {'Enabled' if scan_xss else 'Disabled'}")
        console.print(f"  CSRF Scanning: {'Enabled' if scan_csrf else 'Disabled'}")
        console.print(f"  Directory Scanning: {'Enabled' if scan_dirs else 'Disabled'}")
        
        if not click.confirm("[bold yellow]Do you want to continue?[/bold yellow]", default=True):
            console.print("[bold yellow]Scan cancelled by user[/bold yellow]")
            return 0
    
    # Create scan options dict
    scan_options = {
        "scan_depth": scan_depth,
        "timeout": timeout,
        "threads": threads,
        "rate_limit": rate_limit,
        "scan_sql": scan_sql,
        "scan_xss": scan_xss,
        "scan_csrf": scan_csrf,
        "scan_dirs": scan_dirs,
        "tamil_brak": tamil_brak
    }
    
    # Display Tamil-Brak mode notice if enabled
    if tamil_brak:
        console.print("[bold red]Tamil-Brak Attack Mode Enabled: [/bold red] This comprehensive attack will perform advanced testing on all site components")
        # Increase scan depth and threads for more thorough scanning
        scan_options["scan_depth"] = max(scan_depth, 3)
        scan_options["threads"] = max(threads, 8)
    
    # Start scan
    console.print(f"[bold green]Starting scan against: {url}[/bold green]")
    start_time = time.time()
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[bold blue]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        console=console
    ) as progress:
        scanner = Scanner(url, scan_options, progress)
        results = scanner.run()
        
    end_time = time.time()
    scan_time = end_time - start_time
    
    # Display scan summary
    console.print(f"[bold green]Scan completed in {scan_time:.2f} seconds[/bold green]")
    
    # Generate appropriate report
    if output == 'console':
        generate_console_report(results, console)
    elif output == 'html':
        if not output_file:
            parsed_url = urlparse(url)
            domain = parsed_url.netloc.replace(':', '_')
            output_file = f"cyberwolf_report_{domain}_{int(time.time())}.html"
        
        report_path = generate_html_report(url, results, output_file)
        console.print(f"[bold green]HTML report saved to: {report_path}[/bold green]")
    elif output == 'pdf':
        if not output_file:
            parsed_url = urlparse(url)
            domain = parsed_url.netloc.replace(':', '_')
            output_file = f"cyberwolf_report_{domain}_{int(time.time())}.pdf"
            
        report_path = generate_pdf_report(url, results, output_file)
        console.print(f"[bold green]PDF report saved to: {report_path}[/bold green]")
    
    return 0

def main():
    """Main entry point for the CLI"""
    try:
        return cli()
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {str(e)}")
        logger.exception("Unhandled exception")
        return 1

if __name__ == "__main__":
    sys.exit(main())
