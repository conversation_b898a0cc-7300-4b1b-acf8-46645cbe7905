"""
Validation utility functions for CyberWolf
"""

import re
import logging
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

def validate_url(url):
    """
    Validate if a string is a properly formatted URL
    
    Args:
        url (str): URL to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    if not url:
        return False
    
    # Check for valid URL format
    try:
        result = urlparse(url)
        # Must have scheme and netloc (domain)
        if not all([result.scheme, result.netloc]):
            return False
        
        # Scheme must be http or https
        if result.scheme not in ['http', 'https']:
            return False
        
        # Simple domain validation
        domain_pattern = r'^[a-zA-Z0-9][a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,}$'
        if not re.match(domain_pattern, result.netloc.split(':')[0]):
            # Try with IP address pattern
            ip_pattern = r'^(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})$'
            domain = result.netloc.split(':')[0]
            if not re.match(ip_pattern, domain):
                # Check if it's a valid IP address
                if re.match(ip_pattern, domain):
                    # Check octets
                    octets = domain.split('.')
                    if any(int(octet) > 255 for octet in octets):
                        return False
                else:
                    return False
        
        return True
    except Exception as e:
        logger.debug(f"URL validation error: {str(e)}")
        return False

def validate_email(email):
    """
    Validate if a string is a properly formatted email address
    
    Args:
        email (str): Email to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    if not email:
        return False
    
    # Simple email validation pattern
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))
