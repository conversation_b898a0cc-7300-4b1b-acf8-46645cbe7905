"""
Payload Generator Module for CyberWolf

Dynamically generates security testing payloads by creating variations
from base payloads to increase the test coverage.
"""

import os
import random
import re
import string
import itertools
from pathlib import Path

# Base directory for data files
DATA_DIR = Path(__file__).parent.parent / 'data'


def _load_payloads_from_file(filename):
    """
    Load payloads from a file in the data directory
    
    Args:
        filename (str): Name of the file to load
        
    Returns:
        list: List of payloads from the file
    """
    file_path = DATA_DIR / filename
    if not file_path.exists():
        return []
        
    with open(file_path, 'r', encoding='utf-8') as f:
        return [line.strip() for line in f if line.strip() and not line.strip().startswith('#')]


def _generate_variants(payload):
    """
    Generate variants of a payload
    
    Args:
        payload (str): Original payload
        
    Returns:
        list: List of variant payloads
    """
    variants = [payload]
    
    # Case variations for SQL and XSS payloads
    if any(x in payload.lower() for x in ['select', 'union', 'script', 'alert']):
        variants.append(payload.upper())
        variants.append(payload.lower())
        variants.append(payload.capitalize())
        
        # Randomized case for evasion
        random_case = ''.join(random.choice([c.upper(), c.lower()]) for c in payload)
        variants.append(random_case)
    
    # URL encoding variations
    encoded_payload = ''
    for char in payload:
        if char in string.ascii_letters + string.digits + '-_.~':
            encoded_payload += char
        else:
            hex_val = format(ord(char), 'x')
            encoded_payload += '%' + hex_val
    
    variants.append(encoded_payload)
    
    # Double URL encoding for deeper evasion
    double_encoded_payload = ''
    for char in encoded_payload:
        if char == '%':
            double_encoded_payload += '%25'
        else:
            double_encoded_payload += char
    
    variants.append(double_encoded_payload)
    
    # Unicode encoding variations for XSS payloads
    if '<script>' in payload or 'alert' in payload:
        # Unicode variations
        script_unicode = payload.replace('<script>', '\\u003cscript\\u003e').replace('</script>', '\\u003c/script\\u003e')
        variants.append(script_unicode)
        
        # HTML entity encoding
        script_entities = payload.replace('<', '&lt;').replace('>', '&gt;')
        variants.append(script_entities)
        
        # Decimal HTML encoding
        script_decimal = ''
        for char in payload:
            script_decimal += '&#' + str(ord(char)) + ';'
        variants.append(script_decimal)
        
        # Hexadecimal HTML encoding
        script_hex = ''
        for char in payload:
            script_hex += '&#x' + format(ord(char), 'x') + ';'
        variants.append(script_hex)
    
    # Add space and comment variations for SQL payloads
    if any(x in payload.lower() for x in ['select', 'union', 'from', 'where']):
        # Add unusual whitespace
        sql_space_variant = re.sub(r'\s+', ' /**/', payload)
        variants.append(sql_space_variant)
        
        # Comment padding within keywords
        sql_comment_variant = payload.replace('SELECT', 'SEL/**/ECT').replace('UNION', 'UN/**/ION')
        variants.append(sql_comment_variant)
        
        # Add parentheses in SQL expressions
        if 'SELECT' in payload.upper():
            sql_paren_variant = payload.upper().replace('SELECT', 'SELECT (')
            sql_paren_variant = sql_paren_variant.replace(' FROM', ') FROM')
            variants.append(sql_paren_variant)
    
    return list(set(variants))  # Remove duplicates


def _generate_hybrid_payloads(sql_payloads, xss_payloads, path_payloads, max_hybrids=1000):
    """
    Generate hybrid payloads combining multiple attack techniques
    
    Args:
        sql_payloads (list): List of SQL injection payloads
        xss_payloads (list): List of XSS payloads
        path_payloads (list): List of path traversal payloads
        max_hybrids (int): Maximum number of hybrid payloads to generate
        
    Returns:
        list: List of hybrid payloads
    """
    hybrids = []
    
    # Limit the number of base payloads to use for combinations
    sql_sample = random.sample(sql_payloads, min(20, len(sql_payloads)))
    xss_sample = random.sample(xss_payloads, min(20, len(xss_payloads)))
    path_sample = random.sample(path_payloads, min(10, len(path_payloads)))
    
    # Generate SQL+XSS hybrids
    for sql, xss in itertools.islice(itertools.product(sql_sample, xss_sample), max_hybrids // 3):
        hybrid = f"{sql}{xss}"
        hybrids.append(hybrid)
    
    # Generate SQL+Path hybrids
    for sql, path in itertools.islice(itertools.product(sql_sample, path_sample), max_hybrids // 3):
        hybrid = f"{path}{sql}"
        hybrids.append(hybrid)
    
    # Generate XSS+Path hybrids
    for xss, path in itertools.islice(itertools.product(xss_sample, path_sample), max_hybrids // 3):
        hybrid = f"{path}{xss}"
        hybrids.append(hybrid)
    
    return hybrids[:max_hybrids]


def generate_dynamic_payloads(max_payloads=100000):
    """
    Generate a large number of dynamic payloads for security testing
    
    Args:
        max_payloads (int): Maximum number of payloads to generate
        
    Returns:
        list: List of generated payloads
    """
    # Load existing payloads
    sql_payloads = _load_payloads_from_file('sql_payloads.txt')
    xss_payloads = _load_payloads_from_file('xss_payloads.txt')
    path_payloads = _load_payloads_from_file('common_paths.txt')
    advanced_payloads = _load_payloads_from_file('advanced_payloads.txt')
    
    # Combine all base payloads
    all_payloads = sql_payloads + xss_payloads + path_payloads + advanced_payloads
    
    # Generate variants for each payload
    all_variants = []
    for payload in all_payloads:
        variants = _generate_variants(payload)
        all_variants.extend(variants)
        
        # Break if we have enough variants
        if len(all_variants) >= max_payloads * 0.7:  # Use 70% for variants
            break
    
    # If we need more payloads, generate hybrid attacks
    if len(all_variants) < max_payloads:
        hybrid_count = max_payloads - len(all_variants)
        hybrid_payloads = _generate_hybrid_payloads(sql_payloads, xss_payloads, path_payloads, hybrid_count)
        all_variants.extend(hybrid_payloads)
    
    # Limit to the requested number of payloads
    return all_variants[:max_payloads]


def save_dynamic_payloads(output_file='dynamic_payloads.txt', max_payloads=100000):
    """
    Generate and save dynamic payloads to a file
    
    Args:
        output_file (str): File to save payloads to
        max_payloads (int): Maximum number of payloads to generate
        
    Returns:
        int: Number of payloads saved
    """
    payloads = generate_dynamic_payloads(max_payloads)
    
    output_path = DATA_DIR / output_file
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(f"# CyberWolf Dynamic Payloads ({len(payloads)} total)\n")
        f.write(f"# Tamil-Brak Attack: Advanced Vulnerability Detection\n")
        f.write(f"# Created by S.Tamilselvan (Cybersecurity Researcher)\n\n")
        
        for payload in payloads:
            f.write(f"{payload}\n")
    
    return len(payloads)


if __name__ == "__main__":
    # Generate payloads when run as a script
    count = save_dynamic_payloads()
    print(f"Generated {count} dynamic payloads for security testing")