"""
Cross-Site Request Forgery (CSRF) Scanner Module for CyberWolf
"""

import os
import re
import time
import logging
import concurrent.futures
from urllib.parse import urljoin

from cyberwolf.utils.http import make_request

logger = logging.getLogger(__name__)

class CSRFScanner:
    """CSRF vulnerability scanner"""
    
    def __init__(self, target_url, options):
        """
        Initialize CSRF scanner
        
        Args:
            target_url (str): Target URL to scan
            options (dict): Scanner options
        """
        self.target_url = target_url
        self.options = options
        self.timeout = options.get("timeout", 30)
        self.threads = options.get("threads", 5)
        self.rate_limit = options.get("rate_limit", 10)
        self.visited_urls = set()
        self.progress_callback = None
        self.last_request_time = 0
        
        # CSRF header/token names commonly used
        self.csrf_token_names = [
            'csrf', 'xsrf', 'token', '_token', 'csrf_token', 'xsrf_token',
            'authenticity_token', '__RequestVerificationToken', 'CSRFToken',
            'XSRFToken', 'requesttoken', 'request_token', 'anti-csrf-token',
            'anti-xsrf-token', 'anticsrf'
        ]
        
        # Common CSRF header names
        self.csrf_headers = [
            'X-CSRF-Token', 'X-XSRF-Token', 'X-CSRFToken', 'X-CSRF',
            'CSRF-Token', 'XSRF-Token', 'CSRFToken', 'XSRFToken',
            'X-RequestVerificationToken', 'RequestVerificationToken'
        ]
    
    def set_progress_callback(self, callback):
        """Set a callback function for progress updates"""
        self.progress_callback = callback
    
    def _enforce_rate_limit(self):
        """Enforce rate limiting for requests"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < (1.0 / self.rate_limit):
            sleep_time = (1.0 / self.rate_limit) - time_since_last
            time.sleep(sleep_time)
            
        self.last_request_time = time.time()
    
    def _extract_forms_from_url(self, url):
        """Extract forms from a URL for testing"""
        forms = []
        
        try:
            response = make_request(url, timeout=self.timeout)
            
            if response.status_code == 200:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(response.text, 'html.parser')
                
                for form in soup.find_all('form'):
                    if form.get('method', '').lower() == 'post':
                        action = form.get('action', '')
                        form_inputs = {}
                        
                        # Look for CSRF tokens in hidden inputs
                        has_csrf_token = False
                        
                        for input_field in form.find_all('input'):
                            input_name = input_field.get('name', '').lower()
                            input_type = input_field.get('type', '').lower()
                            input_value = input_field.get('value', '')
                            
                            if input_name:
                                form_inputs[input_name] = input_value
                                
                                # Check if this input looks like a CSRF token
                                if (input_type == 'hidden' and 
                                    any(token_name in input_name.lower() for token_name in self.csrf_token_names)):
                                    has_csrf_token = True
                        
                        form_url = url if not action else urljoin(url, action)
                        forms.append({
                            'url': form_url,
                            'inputs': form_inputs,
                            'has_csrf_token': has_csrf_token
                        })
            
            return forms
        except Exception as e:
            logger.debug(f"Error extracting forms from {url}: {str(e)}")
            return forms
    
    def _check_form_csrf(self, form):
        """Check a form for CSRF vulnerabilities"""
        results = []
        form_url = form['url']
        inputs = form['inputs']
        has_csrf_token = form['has_csrf_token']
        
        # Initial check based on presence of CSRF token in form
        if not has_csrf_token:
            # Try to submit the form to see if CSRF protection exists elsewhere
            self._enforce_rate_limit()
            
            try:
                # Check headers for CSRF tokens
                response = make_request(form_url, timeout=self.timeout)
                has_csrf_header = False
                
                for header in self.csrf_headers:
                    if header in response.headers:
                        has_csrf_header = True
                        break
                
                # If no CSRF header, try a test submission
                if not has_csrf_header:
                    test_response = make_request(form_url, method='post', data=inputs, timeout=self.timeout)
                    
                    # Check if form submission was accepted without a CSRF token
                    # This is a simplistic check and might produce false positives
                    if test_response.status_code < 400:
                        results.append({
                            "url": form_url,
                            "severity": "medium",
                            "description": "Potential CSRF vulnerability: POST form without CSRF protection detected. Form submission was accepted without a CSRF token.",
                            "evidence": "Form does not contain a CSRF token and does not use CSRF headers.",
                            "remediation": "Implement CSRF protection using tokens, same-site cookies, or SameSite cookie attribute. Check for valid referer/origin headers."
                        })
            except Exception as e:
                logger.debug(f"Error testing CSRF on form {form_url}: {str(e)}")
        
        return results
    
    def _check_cookie_settings(self):
        """Check cookie security settings related to CSRF protection"""
        results = []
        
        try:
            response = make_request(self.target_url, timeout=self.timeout)
            cookies = response.cookies
            
            if not cookies:
                return results
            
            # Check SameSite attribute
            vulnerable_cookies = []
            for cookie in cookies:
                same_site = cookie.get_nonstandard_attr('SameSite')
                if not same_site or same_site.lower() == 'none':
                    vulnerable_cookies.append(cookie.name)
            
            if vulnerable_cookies:
                results.append({
                    "url": self.target_url,
                    "severity": "low",
                    "description": f"Cookies without SameSite attribute or with SameSite=None detected: {', '.join(vulnerable_cookies)}. This may lead to CSRF vulnerabilities.",
                    "evidence": f"Cookie settings for: {', '.join(vulnerable_cookies)}",
                    "remediation": "Set the SameSite attribute to 'Lax' or 'Strict' for all cookies that affect authentication or session state."
                })
        except Exception as e:
            logger.debug(f"Error checking cookie settings: {str(e)}")
        
        return results
    
    def scan(self):
        """
        Scan for CSRF vulnerabilities
        
        Returns:
            list: List of discovered vulnerabilities
        """
        results = []
        self.visited_urls = set()
        
        # Extract forms from the main page and subpages
        response = make_request(self.target_url, timeout=self.timeout)
        forms = self._extract_forms_from_url(self.target_url)
        
        # Extract links and check additional pages
        additional_urls = []
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, 'html.parser')
            
            for a_tag in soup.find_all('a', href=True):
                href = a_tag['href']
                url = urljoin(self.target_url, href)
                
                # Only check URLs on the same domain
                if url.startswith(self.target_url) and url not in self.visited_urls:
                    additional_urls.append(url)
                    self.visited_urls.add(url)
                    
                # Limit the number of additional URLs to check
                if len(additional_urls) >= 5:
                    break
        except Exception as e:
            logger.debug(f"Error extracting links: {str(e)}")
        
        # Extract forms from additional pages
        for url in additional_urls:
            try:
                additional_forms = self._extract_forms_from_url(url)
                forms.extend(additional_forms)
            except Exception as e:
                logger.debug(f"Error processing additional URL {url}: {str(e)}")
        
        # Check cookie settings for CSRF protection
        cookie_results = self._check_cookie_settings()
        results.extend(cookie_results)
        
        # Check each form for CSRF vulnerabilities
        total_forms = len(forms)
        completed = 0
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.threads) as executor:
            future_to_form = {executor.submit(self._check_form_csrf, form): form for form in forms}
            
            for future in concurrent.futures.as_completed(future_to_form):
                completed += 1
                
                if self.progress_callback and total_forms > 0:
                    progress = int((completed / total_forms) * 100)
                    self.progress_callback(progress)
                    
                try:
                    form_results = future.result()
                    results.extend(form_results)
                except Exception as e:
                    logger.error(f"Error checking form for CSRF: {str(e)}")
        
        # If we haven't found any forms or vulnerabilities, report this
        if not forms and not results:
            if self.progress_callback:
                self.progress_callback(100)
            
            # Add a note that no CSRF vulnerabilities were found
            results.append({
                "url": self.target_url,
                "severity": "info",
                "description": "No CSRF vulnerabilities were detected. This might be because the site doesn't have POST forms, or because it's properly secured against CSRF.",
                "remediation": "Continue to maintain secure coding practices."
            })
        
        return results
