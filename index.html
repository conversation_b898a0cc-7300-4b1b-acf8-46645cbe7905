<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CyberWolf - Website Vulnerability Scanner</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-dark.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #0d6efd;
            --secondary-color: #6c757d;
            --success-color: #198754;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #0dcaf0;
            --dark-color: #212529;
            --light-color: #f8f9fa;
        }
        
        body {
            background-color: #1a1a1a;
            color: #e9ecef;
            font-family: 'Se<PERSON>e <PERSON>', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
        }

        p {
            color: #e9ecef !important;
        }

        .lead {
            color: #f8f9fa !important;
            font-weight: 400;
        }
        
        .navbar {
            background: linear-gradient(135deg, #10142a 0%, #150327 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .hero-section {
            background: linear-gradient(135deg, #0a0f21 0%, #150c1e 100%);
            padding: 4rem 0;
            margin-bottom: 3rem;
        }
        
        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .hero-subtitle {
            font-size: 1.5rem;
            opacity: 0.9;
        }
        
        .card {
            background-color: #2d3748;
            border: 1px solid #4a5568;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            color: #e9ecef;
        }

        .card p {
            color: #e9ecef !important;
        }

        .card-body p {
            color: #e9ecef !important;
        }

        .text-muted {
            color: #adb5bd !important;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        
        .card-header {
            background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
            border-bottom: 1px solid #4a5568;
            font-weight: 600;
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }
        
        .code-block {
            background-color: #1e2124;
            border: 1px solid #4a5568;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            overflow-x: auto;
        }
        
        .table-dark {
            background-color: #2d3748;
        }
        
        .table-dark th {
            background-color: #4a5568;
            border-color: #4a5568;
        }
        
        .badge-custom {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 500;
        }
        
        .section-divider {
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            margin: 3rem 0;
        }
        
        .toc {
            position: sticky;
            top: 2rem;
            max-height: calc(100vh - 4rem);
            overflow-y: auto;
        }
        
        .toc a {
            color: #cbd5e0;
            text-decoration: none;
            padding: 0.25rem 0;
            display: block;
            border-left: 3px solid transparent;
            padding-left: 1rem;
            transition: all 0.3s ease;
        }
        
        .toc a:hover {
            color: var(--primary-color);
            border-left-color: var(--primary-color);
            padding-left: 1.5rem;
        }
        
        .footer {
            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
            padding: 3rem 0;
            margin-top: 4rem;
        }
        
        .highlight {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid var(--primary-color);
            margin: 1rem 0;
            color: #e9ecef;
        }

        .highlight p {
            color: #e9ecef !important;
        }

        .highlight h5 {
            color: #f8f9fa !important;
        }

        .highlight ul li {
            color: #e9ecef !important;
        }
        
        .warning-box {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(255, 193, 7, 0.1) 100%);
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid var(--danger-color);
            margin: 1rem 0;
            color: #e9ecef;
        }

        .warning-box p {
            color: #e9ecef !important;
        }

        .warning-box h4, .warning-box h5 {
            color: #f8f9fa !important;
        }

        /* Additional text color fixes */
        h1, h2, h3, h4, h5, h6 {
            color: #f8f9fa !important;
        }

        .list-group-item {
            color: #e9ecef !important;
        }

        .small {
            color: #e9ecef !important;
        }

        ol li, ul li {
            color: #e9ecef !important;
        }

        /* LinkedIn button styling */
        .btn-linkedin {
            background-color: #0077b5;
            border-color: #0077b5;
            color: white;
        }

        .btn-linkedin:hover {
            background-color: #005885;
            border-color: #005885;
            color: white;
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            .hero-subtitle {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#top">
                <i class="fas fa-shield-alt me-2"></i>CyberWolf
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="#features">Features</a></li>
                    <li class="nav-item"><a class="nav-link" href="#installation">Installation</a></li>
                    <li class="nav-item"><a class="nav-link" href="#usage">Usage</a></li>
                    <li class="nav-item"><a class="nav-link" href="#examples">Examples</a></li>
                    <li class="nav-item"><a class="nav-link" href="#reports">Reports</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section text-center text-white" id="top">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto">
                    <h1 class="hero-title mb-4">CyberWolf</h1>
                    <p class="hero-subtitle mb-4">Advanced Website Vulnerability Scanner</p>
                    <p class="lead mb-4">
                        A powerful Python-based command-line tool designed for scanning websites for security vulnerabilities. 
                        Helps security researchers and system administrators identify potential security weaknesses in web applications.
                    </p>
                    <div class="d-flex justify-content-center gap-3 flex-wrap">
                        <span class="badge badge-custom bg-primary">100,000+ XSS Vectors</span>
                        <span class="badge badge-custom bg-success">10,000+ SQL Injection Techniques</span>
                        <span class="badge badge-custom bg-warning text-dark">Tamil-Brak Attack Mode</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="container">
        <div class="row">
            <!-- Table of Contents -->
            <div class="col-lg-3">
                <div class="toc">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Table of Contents</h6>
                        </div>
                        <div class="card-body">
                            <a href="#features">Features</a>
                            <a href="#installation">Installation</a>
                            <a href="#usage">Usage</a>
                            <a href="#cli">Command-Line Interface</a>
                            <a href="#web-interface">Web Interface</a>
                            <a href="#examples">Examples</a>
                            <a href="#attack-vectors">Attack Vector Databases</a>
                            <a href="#tamil-brak">Tamil-Brak Mode</a>
                            <a href="#reports">Sample Reports</a>
                            <a href="#technical">Technical Details</a>
                            <a href="#security">Security Warning</a>
                            <a href="#developer">Developer</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9">
                <!-- Features Section -->
                <section id="features" class="mb-5">
                    <h2 class="mb-4">🚀 Features</h2>
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="feature-icon">🛡️</div>
                                    <h5>SQL Injection Detection</h5>
                                    <p>Detects database injection vulnerabilities using advanced techniques</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="feature-icon">⚡</div>
                                    <h5>XSS Scanning</h5>
                                    <p>Identifies script injection vulnerabilities using 100,000+ attack vectors</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="feature-icon">🔒</div>
                                    <h5>CSRF Protection</h5>
                                    <p>Checks for missing Cross-Site Request Forgery protections</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="feature-icon">📁</div>
                                    <h5>Directory Scanning</h5>
                                    <p>Discovers hidden or sensitive paths and directories</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="highlight">
                        <h5>🎯 Tamil-Brak Attack Mode</h5>
                        <p class="mb-0">Performs advanced multi-phase attacks including server information disclosure, path traversal, bug detection, and specialized payload testing.</p>
                    </div>

                    <h4 class="mt-4 mb-3">Additional Features</h4>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item bg-transparent text-light border-secondary">
                            <i class="fas fa-database text-primary me-2"></i>
                            Massive attack vector database (100,000+ XSS payloads, 10,000+ SQL injection techniques)
                        </li>
                        <li class="list-group-item bg-transparent text-light border-secondary">
                            <i class="fas fa-file-alt text-success me-2"></i>
                            Multiple output formats (console, HTML, PDF)
                        </li>
                        <li class="list-group-item bg-transparent text-light border-secondary">
                            <i class="fas fa-layer-group text-warning me-2"></i>
                            Adjustable scan depth and customizable scanning speed
                        </li>
                        <li class="list-group-item bg-transparent text-light border-secondary">
                            <i class="fas fa-tachometer-alt text-info me-2"></i>
                            Multi-threading for faster scans
                        </li>
                        <li class="list-group-item bg-transparent text-light border-secondary">
                            <i class="fas fa-chart-line text-danger me-2"></i>
                            Detailed vulnerability reports with remediation advice
                        </li>
                    </ul>
                </section>

                <hr class="section-divider">

                <!-- Installation Section -->
                <section id="installation" class="mb-5">
                    <h2 class="mb-4">📦 Installation</h2>
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Quick Setup</h5>
                        </div>
                        <div class="card-body">
                            <div class="code-block">
                                <pre><code class="language-bash"># Install dependencies
pip install -r requirements.txt</code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <hr class="section-divider">

                <!-- Usage Section -->
                <section id="usage" class="mb-5">
                    <h2 class="mb-4">🔧 Usage</h2>
                    <p class="lead">CyberWolf can be used in two ways: through the command-line interface (CLI) or through the web interface.</p>

                    <!-- CLI Section -->
                    <div id="cli" class="mb-5">
                        <h3 class="mb-3">💻 Command-Line Interface (CLI)</h3>

                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Basic Usage</h5>
                            </div>
                            <div class="card-body">
                                <div class="code-block">
                                    <pre><code class="language-bash">python cyberwolf.py scan https://example.com</code></pre>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Advanced Options</h5>
                            </div>
                            <div class="card-body">
                                <div class="code-block">
                                    <pre><code class="language-bash">python cyberwolf.py scan [OPTIONS] URL</code></pre>
                                </div>

                                <div class="table-responsive mt-3">
                                    <table class="table table-dark table-striped">
                                        <thead>
                                            <tr>
                                                <th>Option</th>
                                                <th>Description</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><code>-o, --output [console|html|pdf]</code></td>
                                                <td>Output format for the vulnerability report</td>
                                            </tr>
                                            <tr>
                                                <td><code>-f, --output-file TEXT</code></td>
                                                <td>Output file name for HTML/PDF reports</td>
                                            </tr>
                                            <tr>
                                                <td><code>-d, --scan-depth INTEGER</code></td>
                                                <td>Depth of directory scanning (1-5)</td>
                                            </tr>
                                            <tr>
                                                <td><code>-t, --timeout INTEGER</code></td>
                                                <td>Request timeout in seconds</td>
                                            </tr>
                                            <tr>
                                                <td><code>-th, --threads INTEGER</code></td>
                                                <td>Number of threads to use for scanning</td>
                                            </tr>
                                            <tr>
                                                <td><code>-r, --rate-limit INTEGER</code></td>
                                                <td>Requests per second rate limit</td>
                                            </tr>
                                            <tr>
                                                <td><code>--scan-sql / --no-scan-sql</code></td>
                                                <td>Enable/disable SQL injection scanning</td>
                                            </tr>
                                            <tr>
                                                <td><code>--scan-xss / --no-scan-xss</code></td>
                                                <td>Enable/disable XSS scanning</td>
                                            </tr>
                                            <tr>
                                                <td><code>--scan-csrf / --no-scan-csrf</code></td>
                                                <td>Enable/disable CSRF scanning</td>
                                            </tr>
                                            <tr>
                                                <td><code>--scan-dirs / --no-scan-dirs</code></td>
                                                <td>Enable/disable directory scanning</td>
                                            </tr>
                                            <tr>
                                                <td><code>--tamil-brak</code></td>
                                                <td>Enable the powerful Tamil-Brak attack mode</td>
                                            </tr>
                                            <tr>
                                                <td><code>-y, --yes</code></td>
                                                <td>Skip confirmation prompts</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Web Interface Section -->
                    <div id="web-interface" class="mb-5">
                        <h3 class="mb-3">🌐 Web Interface</h3>
                        <p>CyberWolf also provides a user-friendly web interface for conducting scans.</p>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5 class="mb-0">🎛️ Features</h5>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success me-2"></i>Interactive Scan Configuration</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Real-time Progress Tracking</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Vulnerability Type Selection</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Tamil-Brak Advanced Attack Mode</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5 class="mb-0">⚙️ Scan Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-layer-group text-primary me-2"></i>Scan Depth: Light (1) to Comprehensive (5)</li>
                                            <li><i class="fas fa-tachometer-alt text-warning me-2"></i>Thread Control: 1 to 10 threads</li>
                                            <li><i class="fas fa-clock text-info me-2"></i>Request Timeout: 5 to 120 seconds</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="highlight">
                            <h5>🚀 Accessing the Web Interface</h5>
                            <p>To use the web interface, open the <code>templates/index.html</code> file in your web browser. The interface provides:</p>
                            <ol>
                                <li><strong>Target URL Input</strong>: Enter the website URL you want to scan</li>
                                <li><strong>Vulnerability Type Selection</strong>: Choose which types of vulnerabilities to scan for</li>
                                <li><strong>Advanced Settings</strong>: Configure scan depth, threads, and timeout</li>
                                <li><strong>Real-time Progress</strong>: Monitor scan progress with live updates</li>
                                <li><strong>Interactive Results</strong>: View detailed vulnerability reports organized by type</li>
                            </ol>
                        </div>
                    </div>
                </section>

                <hr class="section-divider">

                <!-- Examples Section -->
                <section id="examples" class="mb-5">
                    <h2 class="mb-4">💡 Examples</h2>

                    <!-- CLI Examples -->
                    <div class="mb-5">
                        <h3 class="mb-3">💻 Command-Line Examples</h3>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">Generate HTML Report</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="code-block">
                                            <pre><code class="language-bash">python cyberwolf.py scan -o html -f report.html https://example.com</code></pre>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">Fast Multi-threaded Scan</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="code-block">
                                            <pre><code class="language-bash">python cyberwolf.py scan -th 10 -r 20 https://example.com</code></pre>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">Focused XSS & SQL Scan</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="code-block">
                                            <pre><code class="language-bash">python cyberwolf.py scan --no-scan-csrf --no-scan-dirs https://example.com</code></pre>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">Tamil-Brak Attack Mode</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="code-block">
                                            <pre><code class="language-bash">python cyberwolf.py scan --tamil-brak https://example.com</code></pre>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Web Interface Examples -->
                    <div class="mb-5">
                        <h3 class="mb-3">🌐 Web Interface Usage</h3>
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Step-by-Step Guide</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <ol class="list-group list-group-numbered">
                                            <li class="list-group-item bg-transparent text-light border-secondary">
                                                Open <code>templates/index.html</code> in your web browser
                                            </li>
                                            <li class="list-group-item bg-transparent text-light border-secondary">
                                                Enter the target URL (e.g., <code>https://example.com</code>)
                                            </li>
                                            <li class="list-group-item bg-transparent text-light border-secondary">
                                                Select desired vulnerability types
                                            </li>
                                            <li class="list-group-item bg-transparent text-light border-secondary">
                                                Configure scan settings
                                            </li>
                                            <li class="list-group-item bg-transparent text-light border-secondary">
                                                Click "Start Scan" to begin
                                            </li>
                                            <li class="list-group-item bg-transparent text-light border-secondary">
                                                Monitor progress and view results
                                            </li>
                                        </ol>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Vulnerability Type Selection:</h6>
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check-square text-success me-2"></i> SQL Injection</li>
                                            <li><i class="fas fa-check-square text-success me-2"></i> Cross-Site Scripting (XSS)</li>
                                            <li><i class="fas fa-check-square text-success me-2"></i> Cross-Site Request Forgery (CSRF)</li>
                                            <li><i class="fas fa-check-square text-success me-2"></i> Directory Scanning</li>
                                            <li><i class="fas fa-square text-secondary me-2"></i> Tamil-Brak Advanced Attack Mode</li>
                                        </ul>

                                        <h6 class="mt-3">Scan Configuration:</h6>
                                        <ul class="list-unstyled">
                                            <li><strong>Scan Depth:</strong> Light (1) to Comprehensive (5)</li>
                                            <li><strong>Threads:</strong> 1-10 based on system capabilities</li>
                                            <li><strong>Timeout:</strong> 5-120 seconds</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <hr class="section-divider">

                <!-- Attack Vector Databases Section -->
                <section id="attack-vectors" class="mb-5">
                    <h2 class="mb-4">🗃️ Massive Attack Vector Databases</h2>
                    <p class="lead">CyberWolf includes comprehensive attack vector databases for detecting even the most obscure vulnerabilities.</p>

                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-primary">
                                    <h5 class="mb-0">🚨 XSS Payload Database</h5>
                                    <small>100,000+ Attack Vectors</small>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-code text-warning me-2"></i>Advanced HTML5 attack vectors</li>
                                        <li><i class="fas fa-code text-warning me-2"></i>DOM-based XSS payloads</li>
                                        <li><i class="fas fa-shield-alt text-danger me-2"></i>Filter bypass techniques</li>
                                        <li><i class="fas fa-lock text-info me-2"></i>Encoding variations (URL, HTML, Base64, etc.)</li>
                                        <li><i class="fas fa-bullseye text-success me-2"></i>Context-aware attack patterns</li>
                                        <li><i class="fas fa-mouse-pointer text-primary me-2"></i>Event handler exploits</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-success">
                                    <h5 class="mb-0">💉 SQL Injection Database</h5>
                                    <small>10,000+ Techniques</small>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-search text-primary me-2"></i>Boolean-based blind injections</li>
                                        <li><i class="fas fa-clock text-warning me-2"></i>Time-based blind injections</li>
                                        <li><i class="fas fa-exclamation-triangle text-danger me-2"></i>Error-based techniques</li>
                                        <li><i class="fas fa-link text-info me-2"></i>UNION query attacks</li>
                                        <li><i class="fas fa-layer-group text-secondary me-2"></i>Stacked queries</li>
                                        <li><i class="fas fa-database text-success me-2"></i>Database-specific attacks</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="highlight">
                        <h5>🎯 Intelligent Payload Sampling</h5>
                        <p class="mb-0">The scanner intelligently samples from these databases to test for vulnerabilities without overwhelming the target website. The attack intensity scales automatically based on scan depth settings.</p>
                    </div>
                </section>

                <hr class="section-divider">

                <!-- Tamil-Brak Section -->
                <section id="tamil-brak" class="mb-5">
                    <h2 class="mb-4">⚔️ Tamil-Brak Advanced Attack Mode</h2>
                    <p class="lead">The Tamil-Brak attack mode, developed by S.Tamilselvan, performs a comprehensive multi-phase attack to identify vulnerabilities that might be missed by individual scanners.</p>

                    <div class="row">
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-info">
                                    <h6 class="mb-0">🔍 Phase 1: Information Gathering</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">Identifies technology stack and potential security misconfigurations</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-warning">
                                    <h6 class="mb-0">🐛 Phase 2: Bug Detection</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled small">
                                        <li>• File inclusion vulnerabilities</li>
                                        <li>• Insecure deserialization</li>
                                        <li>• Open redirect vulnerabilities</li>
                                        <li>• Debug information disclosure</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-danger">
                                    <h6 class="mb-0">🎯 Phase 3: Path Traversal</h6>
                                </div>
                                <div class="card-body">
                                    <p class="small">Discovers sensitive paths and directory traversal issues</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="warning-box">
                        <h5>⚡ Concurrent Scanning with Intelligent Rate Limiting</h5>
                        <p class="mb-0">The Tamil-Brak mode uses concurrent scanning with intelligent rate limiting to maximize detection capabilities while minimizing impact on the target server.</p>
                    </div>
                </section>

                <hr class="section-divider">

                <!-- Sample Reports Section -->
                <section id="reports" class="mb-5">
                    <h2 class="mb-4">📊 Sample Reports</h2>

                    <!-- Console Report -->
                    <div class="mb-5">
                        <h3 class="mb-3">💻 Console Report</h3>
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Quick Overview</h5>
                            </div>
                            <div class="card-body">
                                <p>Console reports provide a quick overview of detected vulnerabilities:</p>
                                <div class="code-block">
                                    <pre><code>SCAN SUMMARY

Target: https://example.com
Scan Duration: 45.23 seconds
Total Vulnerabilities: 5
    High Risk: 2
    Medium Risk: 1
    Low Risk: 2</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Web Interface Report -->
                    <div class="mb-5">
                        <h3 class="mb-3">🌐 Web Interface Report</h3>
                        <p>The web interface provides an interactive dashboard with:</p>

                        <div class="row">
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-primary">
                                        <h6 class="mb-0">📋 Summary Section</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-unstyled small">
                                            <li>• <strong>Target</strong>: Scanned website URL</li>
                                            <li>• <strong>Scan Time</strong>: Duration (e.g., "2m 34s")</li>
                                            <li>• <strong>Total Vulnerabilities</strong>: Count of all detected issues</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-warning">
                                        <h6 class="mb-0">⚠️ Risk Overview</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-unstyled small">
                                            <li>• <span class="text-danger">High Risk</span>: Critical vulnerabilities</li>
                                            <li>• <span class="text-warning">Medium Risk</span>: Important security issues</li>
                                            <li>• <span class="text-info">Low Risk</span>: Minor security concerns</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-success">
                                        <h6 class="mb-0">📑 Tabbed Results</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-unstyled small">
                                            <li>• SQL Injection Tab</li>
                                            <li>• XSS Tab</li>
                                            <li>• CSRF Tab</li>
                                            <li>• Directories Tab</li>
                                            <li>• Tamil-Brak Tab</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="highlight">
                            <h5>🔍 Vulnerability Details</h5>
                            <p>Each vulnerability entry includes:</p>
                            <ul>
                                <li><strong>URL</strong>: Specific location of the vulnerability</li>
                                <li><strong>Severity Badge</strong>: Color-coded risk level (Critical/High/Medium/Low)</li>
                                <li><strong>Description</strong>: Clear explanation of the security issue</li>
                                <li><strong>Evidence</strong>: Technical details of how the vulnerability was detected</li>
                                <li><strong>Remediation</strong>: Step-by-step instructions to fix the issue</li>
                            </ul>
                        </div>
                    </div>

                    <!-- HTML and PDF Reports -->
                    <div class="mb-5">
                        <h3 class="mb-3">📄 HTML and PDF Reports</h3>
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Comprehensive Documentation</h5>
                            </div>
                            <div class="card-body">
                                <p>HTML and PDF reports offer comprehensive documentation including:</p>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success me-2"></i>Vulnerability descriptions</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Risk levels and severity ratings</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Affected components and URLs</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success me-2"></i>Detailed remediation suggestions</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Additional security recommendations</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Executive summary for stakeholders</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <hr class="section-divider">

                <!-- Technical Details Section -->
                <section id="technical" class="mb-5">
                    <h2 class="mb-4">⚙️ Web Interface Technical Details</h2>
                    <p class="lead">The web interface (<code>templates/index.html</code>) is built with modern web technologies:</p>

                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-info">
                                    <h5 class="mb-0">🛠️ Technologies</h5>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fab fa-bootstrap text-primary me-2"></i><strong>Bootstrap 5.3</strong>: Modern, responsive UI framework with dark theme</li>
                                        <li><i class="fas fa-mobile-alt text-success me-2"></i><strong>Responsive Design</strong>: Optimized for desktop and mobile devices</li>
                                        <li><i class="fas fa-universal-access text-warning me-2"></i><strong>Accessibility</strong>: ARIA labels and semantic HTML structure</li>
                                        <li><i class="fas fa-sync-alt text-info me-2"></i><strong>Real-time Updates</strong>: Dynamic progress tracking and result display</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-success">
                                    <h5 class="mb-0">🧩 Interface Components</h5>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-form text-primary me-2"></i><strong>Scan Configuration Form</strong>: URL validation and input sanitization</li>
                                        <li><i class="fas fa-chart-line text-warning me-2"></i><strong>Progress Tracking</strong>: Animated progress bar with percentage display</li>
                                        <li><i class="fas fa-chart-pie text-info me-2"></i><strong>Results Dashboard</strong>: Tabbed interface for organized vulnerability display</li>
                                        <li><i class="fas fa-database text-success me-2"></i><strong>Mock Data Integration</strong>: Demonstration data for testing</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <hr class="section-divider">

                <!-- Security Warning Section -->
                <section id="security" class="mb-5">
                    <h2 class="mb-4">⚠️ Important Security Warning</h2>

                    <div class="warning-box">
                        <h4 class="text-danger">⚠️ DISCLAIMER</h4>
                        <p class="mb-3"><strong>This tool is intended for ethical security testing only.</strong> Always ensure you have proper authorization before scanning any website. Unauthorized scanning may be illegal and unethical.</p>
                    </div>

                    <div class="card">
                        <div class="card-header bg-danger">
                            <h5 class="mb-0">📋 Legal and Ethical Guidelines</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Only scan websites you own or have explicit permission to test</li>
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Respect rate limits and avoid overwhelming target servers</li>
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Follow responsible disclosure practices for any vulnerabilities found</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Comply with local laws and regulations regarding security testing</li>
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Use the tool for educational and defensive security purposes only</li>
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Ensure you're using the tool responsibly and ethically</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <hr class="section-divider">

                <!-- Developer Section -->
                <section id="developer" class="mb-5">
                    <h2 class="mb-4">👨‍💻 Developed By</h2>

                    <div class="card">
                        <div class="card-header bg-primary">
                            <h4 class="mb-0">S.Tamilselvan</h4>
                            <small>Cybersecurity Researcher</small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>🎯 Expertise</h5>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-shield-alt text-primary me-2"></i>Web application security and penetration testing</li>
                                        <li><i class="fas fa-code text-success me-2"></i>Developer of the Tamil-Brak advanced attack methodology</li>
                                        <li><i class="fas fa-graduation-cap text-info me-2"></i>Contributor to cybersecurity research and education</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h5>📞 Contact & Support</h5>
                                    <p class="text-muted">For questions, bug reports, or feature requests, please ensure you're using the tool responsibly and ethically.</p>

                                    <div class="mb-3">
                                        <a href="https://www.linkedin.com/in/tamil-selvan-383618304/?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app"
                                           target="_blank"
                                           class="btn btn-primary btn-sm me-2">
                                            <i class="fab fa-linkedin me-2"></i>Connect on LinkedIn
                                        </a>
                                    </div>

                                    <div class="highlight">
                                        <p class="mb-0"><strong>Note:</strong> This tool represents years of research in web application security and vulnerability detection methodologies.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <h5>CyberWolf - Advanced Website Vulnerability Scanner</h5>
                    <p class="text-muted">A powerful Python-based tool for ethical security testing and vulnerability assessment.</p>
                </div>
                <div class="col-md-4 text-end">
                    <p class="text-muted">
                        Developed by <strong>S.Tamilselvan</strong><br>
                        Cybersecurity Researcher
                    </p>
                    <div class="mt-2">
                        <a href="https://www.linkedin.com/in/tamil-selvan-383618304/?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app"
                           target="_blank"
                           class="btn btn-outline-primary btn-sm">
                            <i class="fab fa-linkedin me-1"></i>LinkedIn
                        </a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row">
                <div class="col-md-12 text-center">
                    <p class="text-muted small">
                        &copy; 2025 CyberWolf. This tool is intended for ethical security testing only.
                        Always ensure proper authorization before scanning any website.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add active class to navigation items based on scroll position
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.toc a');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (scrollY >= (sectionTop - 200)) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    </script>
</body>
</html>
