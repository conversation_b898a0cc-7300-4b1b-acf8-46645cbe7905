"""
Directory and Path Scanner Module for CyberWolf
"""

import os
import time
import logging
import concurrent.futures
from urllib.parse import urljoin
import requests
from bs4 import BeautifulSoup

from cyberwolf.utils.http import make_request

logger = logging.getLogger(__name__)

class DirectoryScanner:
    """Directory and path scanning module"""
    
    def __init__(self, target_url, options):
        """
        Initialize directory scanner
        
        Args:
            target_url (str): Target URL to scan
            options (dict): Scanner options
        """
        self.target_url = target_url
        self.options = options
        self.depth = options.get("depth", 2)
        self.timeout = options.get("timeout", 30)
        self.threads = options.get("threads", 5)
        self.rate_limit = options.get("rate_limit", 10)
        self.discovered_paths = set()
        self.visited_urls = set()
        self.common_paths = self._load_common_paths()
        self.progress_callback = None
        self.last_request_time = 0
        
    def _load_common_paths(self):
        """Load common directory paths from file"""
        paths = []
        common_paths_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'common_paths.txt')
        
        try:
            with open(common_paths_file, 'r') as f:
                paths = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        except Exception as e:
            logger.error(f"Error loading common paths file: {str(e)}")
            # Fallback to a minimal list of common paths
            paths = [
                'admin', 'wp-admin', 'administrator', 'login', 'wp-login.php', 'admin.php',
                'backup', 'backups', 'config', 'configs', 'db', 'database',
                'upload', 'uploads', 'tmp', 'temp', 'dev', 'development',
                'test', 'testing', 'staging', 'beta', 'debug', 'old', 'new',
                'logs', 'log', 'private', 'secret', 'secrets', 'api', 'v1', 'v2',
                'wp-content', 'wp-includes', 'includes', 'cgi-bin', 'images', 'img',
                'css', 'js', 'javascript', 'static', 'assets', 'media', 'docs',
                'documentation', 'downloads', 'download', 'file', 'files',
                '.git', '.env', '.htaccess', 'robots.txt', 'sitemap.xml'
            ]
        
        return paths
    
    def set_progress_callback(self, callback):
        """Set a callback function for progress updates"""
        self.progress_callback = callback
    
    def _enforce_rate_limit(self):
        """Enforce rate limiting for requests"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < (1.0 / self.rate_limit):
            sleep_time = (1.0 / self.rate_limit) - time_since_last
            time.sleep(sleep_time)
            
        self.last_request_time = time.time()
    
    def _check_path(self, path):
        """
        Check if a path exists on the target
        
        Args:
            path (str): Path to check
            
        Returns:
            dict or None: Found path information or None if not found
        """
        url = urljoin(self.target_url, path)
        
        if url in self.visited_urls:
            return None
            
        self.visited_urls.add(url)
        self._enforce_rate_limit()
        
        try:
            response = make_request(url, timeout=self.timeout)
            
            if response.status_code < 400:
                severity = "low"
                if response.status_code == 200:
                    # Check if this is a sensitive path
                    sensitive_keywords = ['admin', 'config', 'backup', 'database', 'password', 'secret', 'private', 'auth']
                    if any(keyword in path.lower() for keyword in sensitive_keywords):
                        severity = "high"
                
                return {
                    "url": url,
                    "path": path,
                    "status_code": response.status_code,
                    "severity": severity,
                    "content_type": response.headers.get('Content-Type', ''),
                    "content_length": len(response.content),
                    "description": f"Found accessible path: {path} (Status: {response.status_code})",
                    "remediation": "Verify if this path should be publicly accessible. Consider restricting access if it contains sensitive information."
                }
            return None
        except Exception as e:
            logger.debug(f"Error checking path {path}: {str(e)}")
            return None
    
    def _extract_links(self, url):
        """Extract links from a page for crawling"""
        links = set()
        try:
            response = make_request(url, timeout=self.timeout)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                for a_tag in soup.find_all('a', href=True):
                    href = a_tag['href']
                    full_url = urljoin(url, href)
                    if full_url.startswith(self.target_url) and full_url not in self.visited_urls:
                        links.add(full_url)
            return links
        except Exception as e:
            logger.debug(f"Error extracting links from {url}: {str(e)}")
            return links
    
    def scan(self):
        """
        Scan for accessible directories and paths
        
        Returns:
            list: List of discovered paths and their details
        """
        results = []
        self.visited_urls = set()
        total_paths = len(self.common_paths)
        completed = 0
        
        # First try common paths
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.threads) as executor:
            future_to_path = {executor.submit(self._check_path, path): path for path in self.common_paths}
            
            for future in concurrent.futures.as_completed(future_to_path):
                completed += 1
                progress = int((completed / total_paths) * 100)
                
                if self.progress_callback:
                    self.progress_callback(progress)
                    
                result = future.result()
                if result:
                    results.append(result)
                    self.discovered_paths.add(result["path"])
        
        # Crawl pages if depth > 1
        if self.depth > 1:
            urls_to_crawl = [self.target_url]
            crawled_urls = set()
            current_depth = 1
            
            while urls_to_crawl and current_depth < self.depth:
                new_urls = set()
                
                with concurrent.futures.ThreadPoolExecutor(max_workers=self.threads) as executor:
                    future_to_url = {executor.submit(self._extract_links, url): url for url in urls_to_crawl if url not in crawled_urls}
                    
                    for future in concurrent.futures.as_completed(future_to_url):
                        url = future_to_url[future]
                        crawled_urls.add(url)
                        try:
                            links = future.result()
                            new_urls.update(links)
                        except Exception as e:
                            logger.error(f"Error crawling {url}: {str(e)}")
                
                urls_to_crawl = new_urls
                current_depth += 1
        
        # Sort results by severity
        results.sort(key=lambda x: 0 if x["severity"] == "high" else 1 if x["severity"] == "medium" else 2)
                
        return results
