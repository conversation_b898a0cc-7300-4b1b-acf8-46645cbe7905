"""
HTTP utility functions for CyberWolf
"""

import logging
import random
import requests
from requests.exceptions import RequestException

logger = logging.getLogger(__name__)

# List of common user agents for the requests
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:95.0) Gecko/20100101 Firefox/95.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36 Edg/96.0.1054.62",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"
]

def make_request(url, method='get', headers=None, data=None, timeout=30, allow_redirects=True):
    """
    Make an HTTP request to the specified URL
    
    Args:
        url (str): URL to request
        method (str): HTTP method ('get', 'post', etc.)
        headers (dict): HTTP headers to include
        data (dict): Data to send in the request
        timeout (int): Request timeout in seconds
        allow_redirects (bool): Whether to follow redirects
        
    Returns:
        requests.Response: Response object
        
    Raises:
        RequestException: If the request fails
    """
    if headers is None:
        headers = {}
    
    # Use a random user agent if not specified
    if 'User-Agent' not in headers:
        headers['User-Agent'] = random.choice(USER_AGENTS)
    
    # Add common headers
    if 'Accept' not in headers:
        headers['Accept'] = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
    
    try:
        method = method.lower()
        if method == 'get':
            response = requests.get(
                url,
                headers=headers,
                timeout=timeout,
                allow_redirects=allow_redirects,
                verify=True  # Verify SSL certificates
            )
        elif method == 'post':
            response = requests.post(
                url,
                headers=headers,
                data=data,
                timeout=timeout,
                allow_redirects=allow_redirects,
                verify=True
            )
        elif method == 'head':
            response = requests.head(
                url,
                headers=headers,
                timeout=timeout,
                allow_redirects=allow_redirects,
                verify=True
            )
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")
        
        return response
    except RequestException as e:
        logger.error(f"Request error for {url}: {str(e)}")
        raise
