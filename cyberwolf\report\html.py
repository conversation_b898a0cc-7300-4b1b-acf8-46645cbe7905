"""
HTML report generation for CyberWolf
"""

import os
import time
from datetime import datetime
from urllib.parse import urlparse

def generate_html_report(target_url, results, output_file):
    """
    Generate an HTML report of scan results
    
    Args:
        target_url (str): Target URL
        results (dict): Scan results
        output_file (str): Output file path
        
    Returns:
        str: Path to the generated report
    """
    # Extract results data
    target = target_url
    scan_time = results.get("scan_time", 0)
    vulnerabilities = results.get("vulnerabilities", {})
    summary = results.get("summary", {})
    
    # Generate timestamp
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Start building the HTML
    html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CyberWolf Security Scan Report - {target}</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            border-radius: 5px;
        }}
        h1, h2, h3 {{
            color: #2c3e50;
        }}
        h1 {{
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }}
        .summary {{
            background-color: #eaf6ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }}
        .summary-item {{
            margin-bottom: 10px;
        }}
        .severity {{
            font-weight: bold;
            padding: 3px 8px;
            border-radius: 3px;
            display: inline-block;
            color: white;
        }}
        .critical {{
            background-color: #7d0000;
        }}
        .high {{
            background-color: #e74c3c;
        }}
        .medium {{
            background-color: #f39c12;
        }}
        .low {{
            background-color: #3498db;
        }}
        .info {{
            background-color: #2ecc71;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        th {{
            background-color: #f2f2f2;
            font-weight: bold;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        tr:hover {{
            background-color: #f5f5f5;
        }}
        .recommendations {{
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }}
        .footer {{
            text-align: center;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #eee;
            font-size: 0.9em;
            color: #777;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>CyberWolf Security Scan Report</h1>
        
        <div class="summary">
            <h2>Scan Summary</h2>
            <div class="summary-item"><strong>Target:</strong> {target}</div>
            <div class="summary-item"><strong>Scan Date:</strong> {timestamp}</div>
            <div class="summary-item"><strong>Scan Duration:</strong> {scan_time:.2f} seconds</div>
            <div class="summary-item"><strong>Total Vulnerabilities:</strong> {summary.get('total_vulnerabilities', 0)}</div>
            <div class="summary-item">
                <strong>Risk Breakdown:</strong>
                <div><span class="severity critical">CRITICAL</span> {summary.get('critical_risk', 0)}</div>
                <div><span class="severity high">HIGH</span> {summary.get('high_risk', 0)}</div>
                <div><span class="severity medium">MEDIUM</span> {summary.get('medium_risk', 0)}</div>
                <div><span class="severity low">LOW</span> {summary.get('low_risk', 0)}</div>
            </div>
        </div>
"""
    
    # SQL Injection vulnerabilities
    sql_injection_vulns = vulnerabilities.get("sql_injection", [])
    if sql_injection_vulns:
        html += f"""
        <h2>SQL Injection Vulnerabilities</h2>
        <table>
            <tr>
                <th>Severity</th>
                <th>URL/Parameter</th>
                <th>Description</th>
                <th>Remediation</th>
            </tr>
"""
        
        for vuln in sql_injection_vulns:
            severity = vuln.get("severity", "info")
            url_info = vuln.get("url", "N/A")
            if vuln.get("parameter"):
                url_info += f"<br>Parameter: {vuln.get('parameter')}"
                
            html += f"""
            <tr>
                <td><span class="severity {severity}">{severity.upper()}</span></td>
                <td>{url_info}</td>
                <td>{vuln.get("description", "N/A")}</td>
                <td>{vuln.get("remediation", "N/A")}</td>
            </tr>
"""
        
        html += """
        </table>
"""
    
    # XSS vulnerabilities
    xss_vulns = vulnerabilities.get("xss", [])
    if xss_vulns:
        html += f"""
        <h2>Cross-Site Scripting (XSS) Vulnerabilities</h2>
        <table>
            <tr>
                <th>Severity</th>
                <th>URL/Parameter</th>
                <th>Description</th>
                <th>Remediation</th>
            </tr>
"""
        
        for vuln in xss_vulns:
            severity = vuln.get("severity", "info")
            url_info = vuln.get("url", "N/A")
            if vuln.get("parameter"):
                url_info += f"<br>Parameter: {vuln.get('parameter')}"
                
            html += f"""
            <tr>
                <td><span class="severity {severity}">{severity.upper()}</span></td>
                <td>{url_info}</td>
                <td>{vuln.get("description", "N/A")}</td>
                <td>{vuln.get("remediation", "N/A")}</td>
            </tr>
"""
        
        html += """
        </table>
"""
    
    # CSRF vulnerabilities
    csrf_vulns = vulnerabilities.get("csrf", [])
    if csrf_vulns:
        html += f"""
        <h2>Cross-Site Request Forgery (CSRF) Vulnerabilities</h2>
        <table>
            <tr>
                <th>Severity</th>
                <th>URL</th>
                <th>Description</th>
                <th>Remediation</th>
            </tr>
"""
        
        for vuln in csrf_vulns:
            severity = vuln.get("severity", "info")
                
            html += f"""
            <tr>
                <td><span class="severity {severity}">{severity.upper()}</span></td>
                <td>{vuln.get("url", "N/A")}</td>
                <td>{vuln.get("description", "N/A")}</td>
                <td>{vuln.get("remediation", "N/A")}</td>
            </tr>
"""
        
        html += """
        </table>
"""
    
    # Directory vulnerabilities
    dir_vulns = vulnerabilities.get("directories", [])
    if dir_vulns:
        html += f"""
        <h2>Directory/Path Vulnerabilities</h2>
        <table>
            <tr>
                <th>Severity</th>
                <th>URL/Path</th>
                <th>Description</th>
                <th>Remediation</th>
            </tr>
"""
        
        for vuln in dir_vulns:
            severity = vuln.get("severity", "info")
            url_info = vuln.get("url", "N/A")
            if vuln.get("path"):
                url_info += f"<br>Path: {vuln.get('path')}"
                
            html += f"""
            <tr>
                <td><span class="severity {severity}">{severity.upper()}</span></td>
                <td>{url_info}</td>
                <td>{vuln.get("description", "N/A")}</td>
                <td>{vuln.get("remediation", "N/A")}</td>
            </tr>
"""
        
        html += """
        </table>
"""

    # Tamil-Brak attack vulnerabilities
    tamil_brak_vulns = vulnerabilities.get("tamil_brak", [])
    if tamil_brak_vulns:
        html += f"""
        <h2>Tamil-Brak Advanced Attack Vulnerabilities</h2>
        <table>
            <tr>
                <th>Severity</th>
                <th>URL</th>
                <th>Description</th>
                <th>Remediation</th>
            </tr>
"""
        
        for vuln in tamil_brak_vulns:
            severity = vuln.get("severity", "info")
            url_info = vuln.get("url", "N/A")
            if vuln.get("evidence"):
                url_info += f"<br>Evidence: {vuln.get('evidence')}"
                
            html += f"""
            <tr>
                <td><span class="severity {severity}">{severity.upper()}</span></td>
                <td>{url_info}</td>
                <td>{vuln.get("description", "N/A")}</td>
                <td>{vuln.get("remediation", "N/A")}</td>
            </tr>
"""
        
        html += """
        </table>
"""
    
    # Recommendations section
    html += """
        <div class="recommendations">
            <h2>Security Recommendations</h2>
            
            <h3>General Security Best Practices</h3>
            <ul>
                <li>Keep all software and libraries up to date</li>
                <li>Implement proper input validation and output encoding</li>
                <li>Use Content Security Policy (CSP) headers</li>
                <li>Implement strong password policies</li>
                <li>Enable HTTPS across the entire site</li>
                <li>Use proper session management and secure cookies</li>
                <li>Implement rate limiting and account lockout mechanisms</li>
                <li>Regularly perform security testing and code reviews</li>
            </ul>
            
            <h3>Next Steps</h3>
            <ul>
                <li>Address any high-risk vulnerabilities immediately</li>
                <li>Consider a more comprehensive security assessment</li>
                <li>Implement a security monitoring solution</li>
                <li>Create a security incident response plan</li>
            </ul>
        </div>
        
        <div class="footer">
            <p>Generated by CyberWolf Security Scanner on {timestamp}</p>
        </div>
    </div>
</body>
</html>
"""
    
    # Write the HTML to the output file
    try:
        with open(output_file, 'w') as f:
            f.write(html)
        
        # Get the absolute path to the file
        abs_path = os.path.abspath(output_file)
        return abs_path
    except Exception as e:
        raise Exception(f"Error writing HTML report: {str(e)}")
