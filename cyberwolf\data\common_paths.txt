# Common web directories and files to check for vulnerabilities
# Admin panels
admin/
administrator/
admin.php
admin.html
wp-admin/
administrator.php
administration/
adm/
_adm/
admin/index.php
admin/login.php
admin/admin.php
joomla/administrator/
admin_area/
panel-administracion/
instadmin/
memberadmin/
administratorlogin/
adminpanel/
admin1.php
admin1.html
admin2.php
admin2.html
controlpanel/
admincontrol/
cms/

# Content Management Systems
wp-login.php
wp-content/
wp-config.php
wp-includes/
wp-content/plugins/
wp-content/themes/
wp-content/uploads/
wp-json/
wp-cron.php
xmlrpc.php
wp-admin/admin-ajax.php
administrator/index.php
administrator/components/
joomla.xml
configuration.php
administrator/manifests/
admin/index.php
moodle/
drupal/
drupal/CHANGELOG.txt
sites/default/settings.php
sites/default/files/
sites/default/private/
modules/system/system.install
sites/all/modules/
sites/all/themes/

# API endpoints
api/
api/v1/
api/v2/
rest/
graphql
graphiql
query
swagger
swagger-ui/
swagger-ui.html
v1/
v2/
v3/
_api/
admin/api/
api/swagger-ui.html
api/docs/
api-docs/
/api/auth
/api/token
/api/login

# Backups and temp files
backup/
backups/
bak/
.bak
.back
.backup
.old
.save
.swp
.tmp
.temp
backup.sql
backup.zip
backup.tar.gz
db_backup
dump.sql
database.sql
config.bak
config.old
.config.bak
.config.old
.env
.env.bak
.env.old
.htaccess.bak
.htaccess.old
config/
configs/
conf/

# Configuration and sensitive files
.htaccess
.htpasswd
web.config
.DS_Store
.git/
.gitignore
.svn/
.idea/
.vscode/
config.php
config.js
config.xml
configuration.xml
settings.php
includes/config.php
conf.php
db.php
db.inc
db.class.php
database.php
database.yml
config.inc
conn.php
sql.php
connection.php
credentials.php
keys.php
secrets.php
dev.php
debug.php
errors.php
errors.log
error_log
debug.log
access.log
php_errors.log
system.log
local.properties
settings.local
config.local
server.properties
production.properties

# Development and debugging
dev/
debug/
install/
install.php
setup.php
setup/
test/
tests/
testing/
beta/
demo/
dev.php
phpinfo.php
info.php
test.php
debug.php
debugger.php

# Server-side includes and scripts
cgi-bin/
cgi/
fcgi-bin/
php/
php5/
php-bin/
cgi-sys/
cgi-local/
cgi-perl/
.cgi
.pl
.py
.sh

# Other common directories
logs/
log/
temp/
tmp/
images/
img/
css/
js/
javascript/
media/
uploads/
upload/
files/
file/
assets/
static/
docs/
doc/
download/
downloads/
private/
data/
db/
database/
sql/
mysql/
forum/
forums/
archive/
archives/

# Common files
robots.txt
sitemap.xml
humans.txt
readme.html
README.txt
readme.md
CHANGELOG.txt
changelog.md
CONTRIBUTING.md
LICENSE.txt
LICENSE.md
SECURITY.md
VERSION
version.txt
crossdomain.xml
