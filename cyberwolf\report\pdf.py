"""
PDF report generation for CyberWolf
"""

import os
import tempfile
from datetime import datetime

from cyberwolf.report.html import generate_html_report

def generate_pdf_report(target_url, results, output_file):
    """
    Generate a PDF report of scan results
    
    Args:
        target_url (str): Target URL
        results (dict): Scan results
        output_file (str): Output file path
        
    Returns:
        str: Path to the generated report
    """
    try:
        # First generate HTML report in a temporary file
        temp_html = tempfile.NamedTemporaryFile(suffix='.html', delete=False)
        temp_html.close()
        
        html_path = generate_html_report(target_url, results, temp_html.name)
        
        try:
            # Try to import and use weasyprint for PDF generation
            from weasyprint import HTML, CSS
            
            # Convert HTML to PDF
            HTML(html_path).write_pdf(output_file)
        except ImportError:
            # Fallback to pdfkit if weasyprint is not available
            try:
                import pdfkit
                pdfkit.from_file(html_path, output_file)
            except ImportError:
                # If neither is available, raise an informative error
                os.remove(temp_html.name)
                raise Exception(
                    "PDF generation requires either weasyprint or pdfkit. "
                    "Please install one of them with: "
                    "pip install weasyprint or pip install pdfkit"
                )
        
        # Clean up temporary HTML file
        os.remove(temp_html.name)
        
        # Get the absolute path to the PDF file
        abs_path = os.path.abspath(output_file)
        return abs_path
    except Exception as e:
        raise Exception(f"Error generating PDF report: {str(e)}")
