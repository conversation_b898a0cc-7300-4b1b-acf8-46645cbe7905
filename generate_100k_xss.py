#!/usr/bin/env python3
"""
Large-Scale XSS Payload Generator for CyberWolf

This script generates 100,000 XSS payloads and combines them with existing
payloads to create a comprehensive XSS testing database.

Usage:
    python generate_100k_xss.py

Author: <PERSON><PERSON> (Cybersecurity Researcher)
"""

import os
import sys
import random
import string
import itertools
from datetime import datetime

def create_variations(base_payloads, target_count=100000):
    """Create variations of base payloads to reach target count"""
    all_payloads = set(base_payloads)
    base_count = len(all_payloads)
    
    print(f"[+] Starting with {base_count} base payloads")
    print(f"[+] Need to generate {target_count - base_count} more payloads")
    
    # Create variations by:
    # 1. Adding random suffixes/prefixes
    # 2. Changing case
    # 3. Adding whitespace
    # 4. Combining payloads
    
    # Random strings to add
    def random_string(length=5):
        return ''.join(random.choice(string.ascii_letters) for _ in range(length))
    
    # Process in batches to avoid memory issues
    batch_size = 1000
    base_list = list(all_payloads)
    
    while len(all_payloads) < target_count:
        batch = []
        
        # Create case variations
        for payload in random.sample(base_list, min(batch_size, len(base_list))):
            if random.random() > 0.5:
                # Change random characters to uppercase/lowercase
                new_payload = ''.join(c.upper() if random.random() > 0.6 else c.lower() for c in payload)
                batch.append(new_payload)
        
        # Add prefix/suffix variations
        for payload in random.sample(base_list, min(batch_size, len(base_list))):
            if random.random() > 0.7:
                # Add random prefix
                prefix = random.choice(["'", '"', '>', '<', '//', '/*', ';', ''])
                batch.append(f"{prefix}{payload}")
            
            if random.random() > 0.7:
                # Add random suffix
                suffix = random.choice(["'", '"', '>', '<', '//', '/*', ';', ''])
                batch.append(f"{payload}{suffix}")
        
        # Add whitespace variations
        for payload in random.sample(base_list, min(batch_size, len(base_list))):
            if random.random() > 0.7 and '<' in payload and '>' in payload:
                # Add whitespace after < or before >
                parts = payload.split('<')
                new_parts = []
                for part in parts:
                    if part and '>' in part:
                        tag_parts = part.split('>', 1)
                        if len(tag_parts) > 1 and random.random() > 0.5:
                            whitespace = random.choice([' ', '\t', '\n', '\r'])
                            new_parts.append(f"{tag_parts[0]}{whitespace}>{tag_parts[1]}")
                        else:
                            new_parts.append(part)
                    else:
                        new_parts.append(part)
                
                if new_parts:
                    batch.append('<'.join(new_parts))
        
        # Create hybrid payloads by combining parts of different payloads
        if len(base_list) >= 2:
            for _ in range(batch_size):
                payload1 = random.choice(base_list)
                payload2 = random.choice(base_list)
                
                # Split at a random position
                if len(payload1) > 3 and len(payload2) > 3:
                    pos1 = random.randint(1, len(payload1) - 1)
                    pos2 = random.randint(1, len(payload2) - 1)
                    
                    # Create hybrids
                    hybrid1 = payload1[:pos1] + payload2[pos2:]
                    hybrid2 = payload2[:pos2] + payload1[pos1:]
                    
                    batch.append(hybrid1)
                    batch.append(hybrid2)
        
        # Add encoding variations (simple URL encoding)
        for payload in random.sample(base_list, min(batch_size // 2, len(base_list))):
            if random.random() > 0.8:
                # URL encode some characters
                chars_to_encode = ['<', '>', '"', "'", ';', '(', ')']
                new_payload = ''
                for c in payload:
                    if c in chars_to_encode and random.random() > 0.7:
                        new_payload += f"%{ord(c):02x}"
                    else:
                        new_payload += c
                batch.append(new_payload)
        
        # Add unique payloads to the set
        before_count = len(all_payloads)
        all_payloads.update(batch)
        added = len(all_payloads) - before_count
        
        # Status update
        print(f"[+] Generated {added} new unique payloads in this batch")
        print(f"[+] Total unique payloads: {len(all_payloads)}/{target_count}")
        
        # If we're adding very few new payloads, regenerate the base list
        # to include the new variations for further mutation
        if added < batch_size // 10:
            base_list = list(all_payloads)
            print(f"[+] Refreshed base payload list with {len(base_list)} payloads")
        
        # Break if we've reached the target or aren't making progress
        if len(all_payloads) >= target_count or added == 0:
            break
    
    return list(all_payloads)[:target_count]

def main():
    # Configuration
    target_count = 100000
    input_file = "cyberwolf/data/xss_payloads.txt"
    output_file = "cyberwolf/data/xss_massive.txt"
    
    print("""
 ██████╗██╗   ██╗██████╗ ███████╗██████╗ ██╗    ██╗ ██████╗ ██╗     ███████╗
██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝██╔══██╗██║    ██║██╔═══██╗██║     ██╔════╝
██║      ╚████╔╝ ██████╔╝█████╗  ██████╔╝██║ █╗ ██║██║   ██║██║     █████╗  
██║       ╚██╔╝  ██╔══██╗██╔══╝  ██╔══██╗██║███╗██║██║   ██║██║     ██╔══╝  
╚██████╗   ██║   ██████╔╝███████╗██║  ██║╚███╔███╔╝╚██████╔╝███████╗██║     
 ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝ ╚══╝╚══╝  ╚═════╝ ╚══════╝╚═╝     
                                               100K XSS GENERATOR v1.0
""")
    
    print(f"[+] Starting large-scale XSS payload generation")
    print(f"[+] Target: {target_count} payloads")
    
    start_time = datetime.now()
    print(f"[+] Generation started at: {start_time}")
    
    # Load existing payloads
    base_payloads = []
    if os.path.exists(input_file):
        with open(input_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    base_payloads.append(line)
    
    print(f"[+] Loaded {len(base_payloads)} base payloads from {input_file}")
    
    # Generate variations to reach target count
    all_payloads = create_variations(base_payloads, target_count)
    
    # Save to file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"# CyberWolf XSS Massive Payload Database ({len(all_payloads)} total)\n")
        f.write(f"# Tamil-Brak Attack: Advanced XSS Detection\n")
        f.write(f"# Created by S.Tamilselvan (Cybersecurity Researcher)\n\n")
        
        for payload in all_payloads:
            f.write(f"{payload}\n")
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    print(f"[+] Successfully generated {len(all_payloads)} XSS payloads")
    print(f"[+] Generation completed at: {end_time}")
    print(f"[+] Total time: {duration}")
    print(f"[+] Payloads saved to: {output_file}")

if __name__ == "__main__":
    main()