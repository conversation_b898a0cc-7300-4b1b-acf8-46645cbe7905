# CyberWolf - Website Vulnerability Scanner

<img src="https://cyberwolf-career-guidance.web.app/logo/WhatsApp%20Image%202025-03-22%20at%2011,19,00%20AM-photoaidcom-cropped.jpeg" alt="CyberWolf Logo" width="120"/>
CyberWolf is a powerful Python-based command-line tool designed for scanning websites for security vulnerabilities. It helps security researchers and system administrators identify potential security weaknesses in web applications.

## Features

CyberWolf can scan for various types of web vulnerabilities:

- **SQL Injection** - Detects database injection vulnerabilities
- **Cross-Site Scripting (XSS)** - Identifies script injection vulnerabilities using 100,000+ attack vectors
- **Cross-Site Request Forgery (CSRF)** - Checks for missing CSRF protections
- **Directory Scanning** - Discovers hidden or sensitive paths
- **Tamil-Brak Attack Mode** - Performs advanced multi-phase attacks including server information disclosure, path traversal, bug detection, and specialized payload testing

Additional features include:

- Massive attack vector database (100,000+ XSS payloads, 10,000+ SQL injection techniques)
- Multiple output formats (console, HTML, PDF)
- Adjustable scan depth
- Customizable scanning speed
- Multi-threading for faster scans
- Detailed vulnerability reports with remediation advice
- Intelligent payload sampling based on scan depth

## Installation

```bash
# Install dependencies
pip install -r requirements.txt
```

## Usage

CyberWolf can be used in two ways: through the command-line interface (CLI) or through the web interface.

### Command-Line Interface (CLI)

#### Basic Usage

```bash
python cyberwolf.py scan https://example.com
```

#### Advanced Options

```bash
python cyberwolf.py scan [OPTIONS] URL
```

| Option | Description |
|--------|-------------|
| `-o, --output [console\|html\|pdf]` | Output format for the vulnerability report |
| `-f, --output-file TEXT` | Output file name for HTML/PDF reports |
| `-d, --scan-depth INTEGER` | Depth of directory scanning (1-5) |
| `-t, --timeout INTEGER` | Request timeout in seconds |
| `-th, --threads INTEGER` | Number of threads to use for scanning |
| `-r, --rate-limit INTEGER` | Requests per second rate limit |
| `--scan-sql / --no-scan-sql` | Enable/disable SQL injection scanning |
| `--scan-xss / --no-scan-xss` | Enable/disable XSS scanning |
| `--scan-csrf / --no-scan-csrf` | Enable/disable CSRF scanning |
| `--scan-dirs / --no-scan-dirs` | Enable/disable directory scanning |
| `--tamil-brak` | Enable the powerful Tamil-Brak attack mode |
| `-y, --yes` | Skip confirmation prompts |

### Web Interface

CyberWolf also provides a user-friendly web interface for conducting scans. The web interface includes:

#### Features:
- **Interactive Scan Configuration**: Easy-to-use form for setting up scans
- **Real-time Progress Tracking**: Live progress bar and status updates during scans
- **Vulnerability Type Selection**: Checkboxes to enable/disable specific scan types:
  - SQL Injection scanning
  - Cross-Site Scripting (XSS) scanning
  - Cross-Site Request Forgery (CSRF) scanning
  - Directory scanning
  - Tamil-Brak Advanced Attack Mode

#### Scan Settings:
- **Scan Depth**: Choose from Light (1) to Comprehensive (5)
- **Thread Control**: Adjustable from 1 to 10 threads for performance tuning
- **Request Timeout**: Configurable timeout from 5 to 120 seconds

#### Results Display:
- **Tabbed Interface**: Organized results by vulnerability type
- **Risk Overview**: Summary showing High, Medium, and Low risk vulnerabilities
- **Detailed Reports**: Each vulnerability includes:
  - Affected URL
  - Severity level
  - Description and evidence
  - Remediation recommendations

#### Accessing the Web Interface

To use the web interface, open the `templates/index.html` file in your web browser. The interface provides:

1. **Target URL Input**: Enter the website URL you want to scan
2. **Vulnerability Type Selection**: Choose which types of vulnerabilities to scan for
3. **Advanced Settings**: Configure scan depth, threads, and timeout
4. **Real-time Progress**: Monitor scan progress with live updates
5. **Interactive Results**: View detailed vulnerability reports organized by type

### Examples

#### Command-Line Examples

Scan a website and generate an HTML report:
```bash
python cyberwolf.py scan -o html -f report.html https://example.com
```

Perform a faster scan with more threads:
```bash
python cyberwolf.py scan -th 10 -r 20 https://example.com
```

Focus only on XSS and SQL injection:
```bash
python cyberwolf.py scan --no-scan-csrf --no-scan-dirs https://example.com
```

Use the powerful Tamil-Brak comprehensive attack mode:
```bash
python cyberwolf.py scan --tamil-brak https://example.com
```

#### Web Interface Usage

1. Open `templates/index.html` in your web browser
2. Enter the target URL (e.g., `https://example.com`)
3. Select desired vulnerability types:
   - ✅ SQL Injection
   - ✅ Cross-Site Scripting (XSS)
   - ✅ Cross-Site Request Forgery (CSRF)
   - ✅ Directory Scanning
   - ⬜ Tamil-Brak Advanced Attack Mode (for comprehensive testing)
4. Configure scan settings:
   - **Scan Depth**: Choose from Light (1) to Comprehensive (5)
   - **Threads**: Adjust from 1-10 based on your system capabilities
   - **Timeout**: Set request timeout (5-120 seconds)
5. Click "Start Scan" to begin the vulnerability assessment
6. Monitor real-time progress and view results in organized tabs

### Massive Attack Vector Databases

CyberWolf includes comprehensive attack vector databases for detecting even the most obscure vulnerabilities:

#### XSS Payload Database (100,000+ Vectors)
- Advanced HTML5 attack vectors
- DOM-based XSS payloads
- Filter bypass techniques
- Encoding variations (URL, HTML, Base64, etc.)
- Context-aware attack patterns
- Event handler exploits

#### SQL Injection Database (10,000+ Techniques)
- Boolean-based blind injections
- Time-based blind injections
- Error-based techniques
- UNION query attacks
- Stacked queries
- Out-of-band exploitation
- Database-specific attacks (MySQL, PostgreSQL, MSSQL, Oracle, SQLite)
- Encoding variations and filter bypasses

The scanner intelligently samples from these databases to test for vulnerabilities without overwhelming the target website. The attack intensity scales automatically based on scan depth settings.

### Tamil-Brak Advanced Attack Mode

The Tamil-Brak attack mode, developed by S.Tamilselvan, performs a comprehensive multi-phase attack to identify vulnerabilities that might be missed by individual scanners:

1. **Server Information Gathering**: Identifies technology stack and potential security misconfigurations
2. **Advanced Bug Detection**: 
   - File inclusion vulnerabilities
   - Insecure deserialization
   - Open redirect vulnerabilities
   - Sensitive information in HTML comments
   - Debug information disclosure
   - Business logic flaws
3. **Path Traversal Testing**: Discovers sensitive paths and directory traversal issues
4. **SQL Injection Testing**: Uses time-based and error-based techniques from the 10,000+ payload database
5. **XSS Detection**: Employs sophisticated filter bypass techniques from the 100,000+ vector database
6. **CSRF Analysis**: Identifies forms lacking proper protection
7. **Infrastructure Analysis**: Detects security misconfigurations and missing security headers

The Tamil-Brak mode uses concurrent scanning with intelligent rate limiting to maximize detection capabilities while minimizing impact on the target server.

## Sample Reports

### Console Report

Console reports provide a quick overview of detected vulnerabilities:

```
SCAN SUMMARY

Target: https://example.com
Scan Duration: 45.23 seconds
Total Vulnerabilities: 5
    High Risk: 2
    Medium Risk: 1
    Low Risk: 2
```

### Web Interface Report

The web interface provides an interactive dashboard with:

#### Summary Section:
- **Target**: Scanned website URL
- **Scan Time**: Duration of the scan (e.g., "2m 34s")
- **Total Vulnerabilities**: Count of all detected issues

#### Risk Overview:
- **High Risk**: Critical vulnerabilities requiring immediate attention
- **Medium Risk**: Important security issues to address
- **Low Risk**: Minor security concerns for improvement

#### Tabbed Results:
- **SQL Injection Tab**: Database injection vulnerabilities
- **XSS Tab**: Cross-site scripting vulnerabilities
- **CSRF Tab**: Cross-site request forgery issues
- **Directories Tab**: Exposed directories and sensitive paths
- **Tamil-Brak Tab**: Advanced attack mode results

Each vulnerability entry includes:
- **URL**: Specific location of the vulnerability
- **Severity Badge**: Color-coded risk level (Critical/High/Medium/Low)
- **Description**: Clear explanation of the security issue
- **Evidence**: Technical details of how the vulnerability was detected
- **Remediation**: Step-by-step instructions to fix the issue

### HTML and PDF Reports

HTML and PDF reports offer comprehensive documentation including:
- Vulnerability descriptions
- Risk levels and severity ratings
- Affected components and URLs
- Detailed remediation suggestions
- Additional security recommendations
- Executive summary for stakeholders

# CyberWolf Usage Guide

## Basic Usage

CyberWolf is a powerful security scanning tool that can detect various types of vulnerabilities in websites. Here's how to use it:

```bash
python cyberwolf.py scan https://example.com
```

## Tamil-Brak Attack Mode

For comprehensive security testing, use the powerful Tamil-Brak attack mode:

```bash
python cyberwolf.py scan --tamil-brak https://example.com
```

This advanced mode performs multi-phase attacks including:
- Server information disclosure detection
- Path traversal vulnerability testing
- Advanced payload testing with specialized techniques
- Infrastructure analysis

## Output Options

You can generate different types of reports:

```bash
# Console output (default)
python cyberwolf.py scan https://example.com --output console

# HTML report
python cyberwolf.py scan https://example.com --output html --output-file report.html

# PDF report
python cyberwolf.py scan https://example.com --output pdf --output-file report.pdf
```

## Example Script

The included `example_scan.py` script provides a user-friendly interface for running scans:

```bash
python example_scan.py
```

It will guide you through:
1. Selecting a target URL
2. Choosing between basic scanning and Tamil-Brak attack mode
3. Selecting output format (console, HTML, or PDF)
4. Saving the report to a file (for HTML/PDF output)

## Web Interface Technical Details

The web interface (`templates/index.html`) is built with:

- **Bootstrap 5.3**: Modern, responsive UI framework with dark theme
- **Progressive Enhancement**: Works without JavaScript for basic functionality
- **Real-time Updates**: Dynamic progress tracking and result display
- **Responsive Design**: Optimized for desktop and mobile devices
- **Accessibility**: ARIA labels and semantic HTML structure

### Interface Components:

1. **Scan Configuration Form**:
   - URL validation and input sanitization
   - Checkbox controls for vulnerability type selection
   - Range sliders for performance tuning
   - Form validation and error handling

2. **Progress Tracking**:
   - Animated progress bar with percentage display
   - Real-time status updates during scanning phases
   - Current operation indicator

3. **Results Dashboard**:
   - Tabbed interface for organized vulnerability display
   - Color-coded severity indicators
   - Expandable vulnerability cards with detailed information
   - Risk summary with numerical counts

4. **Mock Data Integration**:
   - Demonstration data for testing the interface
   - Realistic vulnerability examples
   - Sample remediation recommendations

## Important Security Warning

**DISCLAIMER:** This tool is intended for ethical security testing only. Always ensure you have proper authorization before scanning any website. Unauthorized scanning may be illegal and unethical.

### Legal and Ethical Guidelines:
- Only scan websites you own or have explicit permission to test
- Respect rate limits and avoid overwhelming target servers
- Follow responsible disclosure practices for any vulnerabilities found
- Comply with local laws and regulations regarding security testing
- Use the tool for educational and defensive security purposes only

## Developed By

**S.Tamilselvan** (Cybersecurity Researcher)

### Contact & Support:
- Specialized in web application security and penetration testing
- Developer of the Tamil-Brak advanced attack methodology
- Contributor to cybersecurity research and education

For questions, bug reports, or feature requests, please ensure you're using the tool responsibly and ethically.
