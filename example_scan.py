#!/usr/bin/env python3
"""
CyberWolf Example <PERSON><PERSON>

This script demonstrates how to use CyberWolf for website security scanning
with a focus on the Tamil-Brak attack mode.
"""

import os
import sys
import time
from urllib.parse import urlparse

print("""
 ██████╗██╗   ██╗██████╗ ███████╗██████╗ ██╗    ██╗ ██████╗ ██╗     ███████╗
██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝██╔══██╗██║    ██║██╔═══██╗██║     ██╔════╝
██║      ╚████╔╝ ██████╔╝█████╗  ██████╔╝██║ █╗ ██║██║   ██║██║     █████╗  
██║       ╚██╔╝  ██╔══██╗██╔══╝  ██╔══██╗██║███╗██║██║   ██║██║     ██╔══╝  
╚██████╗   ██║   ██████╔╝███████╗██║  ██║╚███╔███╔╝╚██████╔╝███████╗██║     
 ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝ ╚══╝╚══╝  ╚═════╝ ╚══════╝╚═╝     
                                                         EXAMPLE SCAN SCRIPT
""")

print("\nCYBERWOLF SECURITY SCANNER - DEMONSTRATION")
print("=" * 50)
print("\nThis example will demonstrate how to run a security scan using CyberWolf.")
print("You can modify this script to scan your own targets.\n")

# Default target (can be changed by user)
target_url = "https://example.com"

# Ask user if they want to scan a different URL
use_custom = input("Do you want to scan a custom URL? (y/n) [default: n]: ").lower() == 'y'

if use_custom:
    custom_url = input("Enter the URL to scan (include http:// or https://): ")
    if custom_url:
        target_url = custom_url

# Validate URL format
parsed_url = urlparse(target_url)
if not all([parsed_url.scheme, parsed_url.netloc]):
    print("\n[ERROR] Invalid URL format. Please include http:// or https://")
    sys.exit(1)

print(f"\nTarget URL: {target_url}")

# Select scan type
print("\nSCAN OPTIONS:")
print("1. Basic scan (SQL injection, XSS, CSRF, directory scanning)")
print("2. Full scan with Tamil-Brak attack mode (comprehensive security test)")
scan_choice = input("Select scan type (1/2) [default: 1]: ") or "1"

# Configure output format
print("\nOUTPUT OPTIONS:")
print("1. Console output (results displayed in terminal)")
print("2. HTML report (saved as HTML file)")
print("3. PDF report (saved as PDF file)")
output_choice = input("Select output format (1/2/3) [default: 1]: ") or "1"

output_format = {
    "1": "console", 
    "2": "html",
    "3": "pdf"
}.get(output_choice, "console")

# Set up output file if needed
output_file = None
if output_format in ["html", "pdf"]:
    domain = parsed_url.netloc.replace(':', '_')
    file_ext = "html" if output_format == "html" else "pdf"
    default_filename = f"cyberwolf_report_{domain}_{int(time.time())}.{file_ext}"
    
    output_file = input(f"Enter output filename [default: {default_filename}]: ") or default_filename
    print(f"\nReport will be saved as: {output_file}")

# Build the command
command = f"python cyberwolf.py scan {target_url} --output {output_format}"

if output_file:
    command += f" --output-file {output_file}"

# Add Tamil-Brak attack mode if selected
if scan_choice == "2":
    command += " --tamil-brak"

# Show the command that will be run
print("\n" + "=" * 50)
print(f"COMMAND TO EXECUTE: {command}")
print("=" * 50)

# Ask for confirmation before running
print("\nDISCLAIMER: Only scan websites that you have permission to test.")
print("Unauthorized scanning of websites may be illegal and unethical.")
proceed = input("\nProceed with scan? (y/n) [default: n]: ").lower() == 'y'

if proceed:
    print("\nStarting scan, please wait...\n")
    os.system(command)
    
    if output_format in ["html", "pdf"]:
        print(f"\nScan complete! Report saved to {output_file}")
    else:
        print("\nScan complete!")
else:
    print("\nScan cancelled.")