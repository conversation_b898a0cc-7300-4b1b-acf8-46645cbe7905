"""
Advanced Scanner Module for CyberWolf

This module provides enhanced scanning capabilities for the Tamil-Brak attack mode
"""

import os
import re
import time
import logging
import random
import json
import concurrent.futures
from urllib.parse import urlparse, urljoin, parse_qsl, urlencode, urlunparse
import hashlib

from cyberwolf.utils.http import make_request

logger = logging.getLogger(__name__)

class AdvancedScanner:
    """Advanced vulnerability scanner for Tamil-Brak attack mode"""
    
    def __init__(self, target_url, options):
        """
        Initialize the advanced scanner
        
        Args:
            target_url (str): Target URL to scan
            options (dict): Scanner options
        """
        self.target_url = target_url
        self.options = options
        self.timeout = options.get("timeout", 30)
        self.threads = options.get("threads", 5)
        self.rate_limit = options.get("rate_limit", 10)
        self.last_request_time = 0
        self.visited_urls = set()
        self.progress_callback = None
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        # Initialize various detection patterns
        self._init_detection_patterns()
    
    def _init_detection_patterns(self):
        """Initialize detection patterns for various vulnerability types"""
        # Sensitive information patterns
        self.sensitive_patterns = [
            # API keys and tokens
            r'(?i)api[_-]?key[_-]?[\w\-\.]+["\']?\s*[:=]\s*["\']?[\w\-\.]{20,}["\']?',
            r'(?i)access[_-]?token[_-]?[\w\-\.]+["\']?\s*[:=]\s*["\']?[\w\-\.]{20,}["\']?',
            r'(?i)secret[_-]?key[_-]?[\w\-\.]+["\']?\s*[:=]\s*["\']?[\w\-\.]{20,}["\']?',
            # AWS keys
            r'(?i)AKIA[0-9A-Z]{16}',
            # Database connection strings
            r'(?i)(?:mongodb|postgresql|mysql)://[\w\-\.]+:[^@]+@[\w\-\.]+/[\w\-\.]+',
            # Private keys
            r'-----BEGIN (?:RSA|DSA|EC|PGP) PRIVATE KEY',
            # JWT tokens
            r'eyJ[a-zA-Z0-9\-_]+\.eyJ[a-zA-Z0-9\-_]+\.[a-zA-Z0-9\-_]+',
        ]
        
        # Server information patterns
        self.server_info_patterns = [
            # Server software disclosure
            r'(?i)server\s*:\s*[\w\-\./]+',
            r'(?i)x-powered-by\s*:\s*[\w\-\./]+',
            r'(?i)x-aspnet-version\s*:\s*[\w\-\./]+',
            r'(?i)x-runtime\s*:\s*[\w\-\./]+',
            # Technology information
            r'(?i)<meta\s+name=["\']generator["\'][^>]+content=["\']([^"\']+)["\']',
            r'(?i)Powered by\s+([^\s<]+)',
            # Framework fingerprints
            r'(?i)laravel_session',
            r'(?i)PHPSESSID',
            r'(?i)JSESSIONID',
            r'(?i)ASP.NET_SessionId',
        ]
        
        # Infrastructure vulnerability patterns
        self.infra_vuln_patterns = [
            # Misconfigured CORS
            r'(?i)Access-Control-Allow-Origin\s*:\s*\*',
            # Security headers missing
            r'(?i)Strict-Transport-Security',
            r'(?i)Content-Security-Policy',
            r'(?i)X-Content-Type-Options',
            r'(?i)X-Frame-Options',
            # Debug information
            r'(?i)<\!--\s+DEBUG',
            r'(?i)Exception in thread "main"',
            r'(?i)Stack trace:',
            r'(?i)Error:\s+.*?line\s+\d+',
        ]
        
        # Business logic vulnerability indicators
        self.business_logic_patterns = [
            # Path traversal hints
            r'(?i)\.\./',
            r'(?i)\.\.%2f',
            # Injection indicators
            r'(?i)at\s+com\.',
            r'(?i)at\s+org\.',
            r'(?i)at\s+java\.',
            r'(?i)mysqli_',
            r'(?i)sqlite_',
            r'(?i)pg_',
            # Configuration leaks
            r'(?i)config\.',
            r'(?i)settings\.',
            r'(?i)\.env',
            r'(?i)\.git/',
            r'(?i)\.svn/',
        ]

    def set_progress_callback(self, callback):
        """Set a callback function for progress updates"""
        self.progress_callback = callback
    
    def _enforce_rate_limit(self):
        """Enforce rate limiting for requests"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < (1.0 / self.rate_limit):
            sleep_time = (1.0 / self.rate_limit) - time_since_last
            time.sleep(sleep_time)
            
        self.last_request_time = time.time()
    
    def _extract_links(self, url, html_content):
        """
        Extract links from HTML content
        
        Args:
            url (str): Base URL
            html_content (str): HTML content
            
        Returns:
            list: List of extracted URLs
        """
        links = []
        
        # Extract href attributes using regex
        href_pattern = re.compile(r'<a\s+(?:[^>]*?\s+)?href=(["\'])(.*?)\1', re.IGNORECASE)
        for match in href_pattern.finditer(html_content):
            href = match.group(2)
            if href:
                # Handle relative URLs
                absolute_url = urljoin(url, href)
                
                # Only include URLs from the same domain
                if urlparse(absolute_url).netloc == urlparse(self.target_url).netloc:
                    # Normalize the URL by removing fragments
                    normalized_url = absolute_url.split('#')[0]
                    links.append(normalized_url)
        
        return list(set(links))  # Remove duplicates
    
    def _extract_forms(self, url, html_content):
        """
        Extract forms from HTML content
        
        Args:
            url (str): Base URL
            html_content (str): HTML content
            
        Returns:
            list: List of extracted forms
        """
        forms = []
        
        # Match form tags
        form_pattern = re.compile(r'<form\s+([^>]*)>(.+?)</form>', re.IGNORECASE | re.DOTALL)
        for form_match in form_pattern.finditer(html_content):
            form_attrs = form_match.group(1)
            form_content = form_match.group(2)
            
            # Extract form action and method
            action_match = re.search(r'action=(["\'])(.*?)\1', form_attrs, re.IGNORECASE)
            action = action_match.group(2) if action_match else ''
            
            method_match = re.search(r'method=(["\'])(.*?)\1', form_attrs, re.IGNORECASE)
            method = method_match.group(2).lower() if method_match else 'get'
            
            # Extract input fields
            inputs = {}
            input_pattern = re.compile(r'<input\s+([^>]*)>', re.IGNORECASE)
            for input_match in input_pattern.finditer(form_content):
                input_attrs = input_match.group(1)
                
                # Extract name and value
                name_match = re.search(r'name=(["\'])(.*?)\1', input_attrs, re.IGNORECASE)
                value_match = re.search(r'value=(["\'])(.*?)\1', input_attrs, re.IGNORECASE)
                
                if name_match:
                    name = name_match.group(2)
                    value = value_match.group(2) if value_match else ''
                    inputs[name] = value
            
            # Also get textarea fields
            textarea_pattern = re.compile(r'<textarea\s+([^>]*)>(.+?)</textarea>', re.IGNORECASE | re.DOTALL)
            for textarea_match in textarea_pattern.finditer(form_content):
                textarea_attrs = textarea_match.group(1)
                textarea_value = textarea_match.group(2).strip()
                
                name_match = re.search(r'name=(["\'])(.*?)\1', textarea_attrs, re.IGNORECASE)
                if name_match:
                    name = name_match.group(2)
                    inputs[name] = textarea_value
            
            # Normalize action URL
            action_url = urljoin(url, action)
            
            forms.append({
                'url': action_url,
                'method': method,
                'inputs': inputs
            })
        
        return forms
    
    def _check_reflected_params(self, url):
        """
        Check for reflected parameters in URL
        
        Args:
            url (str): URL to check
            
        Returns:
            list: Vulnerabilities found
        """
        results = []
        parsed_url = urlparse(url)
        parameters = parse_qsl(parsed_url.query)
        
        if not parameters:
            return results
        
        for param, value in parameters:
            # Skip empty values
            if not value:
                continue
                
            # Create a unique test value
            test_value = f"TamilBrak{random.randint(10000, 99999)}"
            
            # Replace the parameter with test value
            new_params = [(p, test_value if p == param else v) for p, v in parameters]
            new_query = urlencode(new_params)
            
            # Create a new URL with the test value
            url_parts = list(parsed_url)
            url_parts[4] = new_query
            test_url = urlunparse(url_parts)
            
            try:
                self._enforce_rate_limit()
                response = make_request(test_url, timeout=self.timeout)
                
                # Check if the test value is reflected in the response
                if test_value in response.text:
                    results.append({
                        "url": url,
                        "parameter": param,
                        "type": "reflected_parameter",
                        "severity": "medium",
                        "description": f"Parameter '{param}' is reflected in the response, which might lead to XSS vulnerabilities",
                        "remediation": "Implement proper output encoding for user-controlled parameters"
                    })
            except Exception as e:
                logger.debug(f"Error checking reflected parameter {param}: {str(e)}")
        
        return results
    
    def _find_information_disclosure(self, url, response):
        """
        Find information disclosure in responses
        
        Args:
            url (str): URL being tested
            response: Response object
            
        Returns:
            list: Vulnerabilities found
        """
        results = []
        
        # Check headers
        for header, value in response.headers.items():
            # Look for server information
            for pattern in self.server_info_patterns:
                if re.search(pattern, f"{header}: {value}"):
                    results.append({
                        "url": url,
                        "type": "information_disclosure",
                        "severity": "low",
                        "description": f"Server information disclosure in header: {header}: {value}",
                        "remediation": "Configure your server to hide version information in headers"
                    })
        
        # Check HTML content
        for pattern in self.sensitive_patterns:
            matches = re.findall(pattern, response.text)
            for match in matches:
                results.append({
                    "url": url,
                    "type": "sensitive_information",
                    "severity": "high",
                    "description": f"Potential sensitive information disclosed: {match[:20]}...",
                    "evidence": f"{match[:50]}...",
                    "remediation": "Remove sensitive information from client-side code"
                })
        
        # Check for infrastructure vulnerabilities
        missing_security_headers = []
        for pattern in self.infra_vuln_patterns[:5]:  # Check first 5 patterns (security headers)
            header_name = re.sub(r'(?i)(.+)', r'\1', pattern)
            if header_name and header_name not in response.headers:
                missing_security_headers.append(header_name)
        
        if missing_security_headers:
            results.append({
                "url": url,
                "type": "missing_security_headers",
                "severity": "medium",
                "description": f"Missing security headers: {', '.join(missing_security_headers)}",
                "remediation": "Implement proper security headers to improve web security"
            })
        
        return results
    
    def _check_csrf_vulnerabilities(self, url, forms):
        """
        Check for CSRF vulnerabilities in forms
        
        Args:
            url (str): URL being tested
            forms (list): Forms to check
            
        Returns:
            list: Vulnerabilities found
        """
        results = []
        
        for form in forms:
            # Skip forms that don't modify data
            if form['method'] != 'post':
                continue
                
            # Check if the form has CSRF token
            has_csrf_token = False
            for input_name in form['inputs']:
                if any(token_name in input_name.lower() for token_name in ['csrf', 'token', 'nonce', '_token']):
                    has_csrf_token = True
                    break
            
            if not has_csrf_token:
                results.append({
                    "url": url,
                    "type": "csrf_vulnerability",
                    "severity": "medium",
                    "description": f"Form at {form['url']} lacks CSRF protection",
                    "evidence": f"POST form without CSRF token: {list(form['inputs'].keys())}",
                    "remediation": "Implement CSRF tokens for all forms that modify data"
                })
        
        return results
    
    def _check_for_bugs(self, url, response):
        """
        Check for various bugs and vulnerabilities
        
        Args:
            url (str): URL being tested
            response: Response object
            
        Returns:
            list: Vulnerabilities found
        """
        results = []
        
        # Parse the URL and response
        parsed_url = urlparse(url)
        
        # Extract forms and links for further testing
        forms = self._extract_forms(url, response.text)
        links = self._extract_links(url, response.text)
        
        # 1. Check for information disclosure
        info_disclosure = self._find_information_disclosure(url, response)
        results.extend(info_disclosure)
        
        # 2. Check for CSRF vulnerabilities
        csrf_vulns = self._check_csrf_vulnerabilities(url, forms)
        results.extend(csrf_vulns)
        
        # 3. Check for reflected parameters (potential XSS)
        reflected_params = self._check_reflected_params(url)
        results.extend(reflected_params)
        
        # 4. Check for business logic vulnerabilities
        for pattern in self.business_logic_patterns:
            matches = re.findall(pattern, response.text)
            if matches:
                results.append({
                    "url": url,
                    "type": "potential_business_logic_vulnerability",
                    "severity": "medium",
                    "description": f"Potential vulnerability indicator found: {pattern}",
                    "evidence": f"Found pattern: {matches[0][:50]}...",
                    "remediation": "Review the code for potential business logic vulnerabilities"
                })
        
        # 5. Check for debug information
        debug_patterns = [
            r'(?i)DEBUG',
            r'(?i)TRACE',
            r'(?i)console\.log\(',
            r'(?i)System\.out\.print',
            r'(?i)var_dump\(',
            r'(?i)print_r\(',
            r'(?i)alert\(',
        ]
        
        for pattern in debug_patterns:
            matches = re.findall(pattern, response.text)
            if matches:
                results.append({
                    "url": url,
                    "type": "debug_information",
                    "severity": "low",
                    "description": "Debug information found in page source",
                    "evidence": f"Debug pattern: {matches[0][:50]}...",
                    "remediation": "Remove debug information from production code"
                })
                break  # One debug finding is enough
                
        # 6. Check for file inclusion vulnerabilities
        file_inclusion_patterns = [
            r'(?i)include\s*\([\'"]',
            r'(?i)require\s*\([\'"]',
            r'(?i)include_once\s*\([\'"]',
            r'(?i)require_once\s*\([\'"]',
            r'(?i)file_get_contents\s*\([\'"]',
            r'(?i)fopen\s*\([\'"]',
        ]
        
        for pattern in file_inclusion_patterns:
            matches = re.findall(pattern, response.text)
            if matches:
                results.append({
                    "url": url,
                    "type": "potential_file_inclusion",
                    "severity": "high",
                    "description": "Potential file inclusion vulnerability",
                    "evidence": f"Found pattern: {matches[0][:50]}...",
                    "remediation": "Validate file paths and use whitelisting approach for file operations"
                })
                break
                
        # 7. Check for insecure deserialization hints
        deserialization_patterns = [
            r'(?i)unserialize\s*\(',
            r'(?i)Marshal\.load',
            r'(?i)pickle\.load',
            r'(?i)yaml\.load',
            r'(?i)readObject\s*\(',
        ]
        
        for pattern in deserialization_patterns:
            matches = re.findall(pattern, response.text)
            if matches:
                results.append({
                    "url": url,
                    "type": "insecure_deserialization",
                    "severity": "high",
                    "description": "Potential insecure deserialization vulnerability",
                    "evidence": f"Found pattern: {matches[0][:50]}...",
                    "remediation": "Use safe alternatives or implement integrity checks before deserialization"
                })
                break
                
        # 8. Check for open redirects
        open_redirect_params = ['url', 'redirect', 'next', 'return', 'returnto', 'goto', 'target']
        query_params = dict(parse_qsl(parsed_url.query))
        
        for param in open_redirect_params:
            if param in query_params and (
                query_params[param].startswith('http') or 
                query_params[param].startswith('//') or
                query_params[param].startswith('www.')
            ):
                results.append({
                    "url": url,
                    "type": "open_redirect",
                    "severity": "medium",
                    "description": f"Potential open redirect vulnerability in parameter '{param}'",
                    "evidence": f"Parameter value: {query_params[param]}",
                    "remediation": "Implement a whitelist of allowed redirect URLs or use relative paths"
                })
                
        # 9. Check for sensitive information in HTML comments
        comment_pattern = r'<!--(.*?)-->'
        comments = re.findall(comment_pattern, response.text, re.DOTALL)
        sensitive_patterns_in_comments = [
            r'(?i)password',
            r'(?i)username',
            r'(?i)api[-_]?key',
            r'(?i)secret',
            r'(?i)token',
            r'(?i)credentials',
            r'(?i)database',
            r'(?i)TODO',
            r'(?i)FIXME',
        ]
        
        for comment in comments:
            for pattern in sensitive_patterns_in_comments:
                if re.search(pattern, comment):
                    results.append({
                        "url": url,
                        "type": "sensitive_information_in_comments",
                        "severity": "medium",
                        "description": "Sensitive information found in HTML comments",
                        "evidence": f"Comment containing: {comment[:100]}...",
                        "remediation": "Remove sensitive information from HTML comments"
                    })
                    break
        
        return results
    
    def scan_url(self, url):
        """
        Scan a single URL for vulnerabilities
        
        Args:
            url (str): URL to scan
            
        Returns:
            list: Vulnerabilities found
        """
        vulnerabilities = []
        
        # Skip if we've already visited this URL
        if url in self.visited_urls:
            return vulnerabilities
            
        self.visited_urls.add(url)
        
        try:
            self._enforce_rate_limit()
            response = make_request(url, timeout=self.timeout, headers=self.headers)
            
            # Skip non-successful responses
            if response.status_code >= 400:
                return vulnerabilities
                
            # Check for various vulnerabilities
            vulns = self._check_for_bugs(url, response)
            vulnerabilities.extend(vulns)
            
            # Extract additional URLs for testing
            links = self._extract_links(url, response.text)
            return vulnerabilities, links
            
        except Exception as e:
            logger.debug(f"Error scanning URL {url}: {str(e)}")
            return vulnerabilities, []
    
    def scan(self, max_urls=50):
        """
        Run the advanced scanner
        
        Args:
            max_urls (int): Maximum number of URLs to scan
            
        Returns:
            list: Vulnerabilities found
        """
        results = []
        
        # Start with the target URL
        urls_to_scan = [self.target_url]
        scanned_count = 0
        
        # Use a thread pool for concurrent scanning
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.threads) as executor:
            while urls_to_scan and scanned_count < max_urls:
                # Take a batch of URLs based on thread count
                batch_size = min(self.threads, len(urls_to_scan), max_urls - scanned_count)
                batch = urls_to_scan[:batch_size]
                urls_to_scan = urls_to_scan[batch_size:]
                
                # Submit scan tasks
                future_to_url = {executor.submit(self.scan_url, url): url for url in batch}
                
                # Process results as they complete
                for future in concurrent.futures.as_completed(future_to_url):
                    url = future_to_url[future]
                    scanned_count += 1
                    
                    # Update progress if callback is provided
                    if self.progress_callback:
                        progress = int((scanned_count / max_urls) * 100)
                        self.progress_callback(progress)
                    
                    try:
                        scan_result = future.result()
                        if isinstance(scan_result, tuple) and len(scan_result) == 2:
                            vulnerabilities, new_links = scan_result
                            results.extend(vulnerabilities)
                            
                            # Add new links to the queue, but prevent exceeding max_urls
                            remaining_slots = max_urls - scanned_count - len(urls_to_scan)
                            if remaining_slots > 0:
                                # Filter out already visited URLs
                                new_links = [link for link in new_links if link not in self.visited_urls]
                                # Add up to the remaining slots
                                urls_to_scan.extend(new_links[:remaining_slots])
                        else:
                            # Handle case where scan_result is not a tuple
                            results.extend(scan_result if isinstance(scan_result, list) else [])
                    except Exception as e:
                        logger.error(f"Error processing scan result for {url}: {str(e)}")
        
        # Deduplicate results based on URL and type
        unique_results = []
        seen = set()
        
        for vuln in results:
            key = f"{vuln['url']}:{vuln['type']}"
            if key not in seen:
                seen.add(key)
                unique_results.append(vuln)
        
        return unique_results

def scan_with_concurrency(target_url, options, progress_callback=None):
    """
    Run an advanced scan with concurrency
    
    Args:
        target_url (str): Target URL to scan
        options (dict): Scanner options
        progress_callback (callable): Progress callback function
        
    Returns:
        list: Vulnerabilities found
    """
    scanner = AdvancedScanner(target_url, options)
    
    if progress_callback:
        scanner.set_progress_callback(progress_callback)
        
    # Determine maximum URLs to scan based on scan depth
    scan_depth = options.get("scan_depth", 2)
    max_urls = min(10 * scan_depth, 50)  # Limit to 50 URLs maximum
    
    return scanner.scan(max_urls=max_urls)