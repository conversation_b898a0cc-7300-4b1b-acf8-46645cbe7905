"""
Console report generation for CyberWolf
"""

from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from rich.markdown import Markdown
from rich.console import Group

def generate_vulnerability_table(vulnerabilities, title, console):
    """
    Generate a vulnerability table for the console
    
    Args:
        vulnerabilities (list): List of vulnerabilities
        title (str): Table title
        console (rich.console.Console): Console to print to
        
    Returns:
        rich.table.Table: Generated table
    """
    table = Table(title=title, expand=True)
    
    table.add_column("Severity", style="bold")
    table.add_column("URL/Parameter")
    table.add_column("Description")
    table.add_column("Remediation")
    
    if not vulnerabilities:
        table.add_row("N/A", "No vulnerabilities found", "No issues detected", "Maintain current security practices")
    else:
        for vuln in vulnerabilities:
            severity = vuln.get("severity", "info")
            severity_style = {
                "high": "bold red",
                "medium": "bold yellow",
                "low": "bold blue",
                "info": "bold green"
            }.get(severity, "bold")
            
            url_info = vuln.get("url", "N/A")
            if vuln.get("parameter"):
                url_info += f"\nParameter: {vuln.get('parameter')}"
            
            table.add_row(
                severity.upper(),
                url_info,
                vuln.get("description", "N/A"),
                vuln.get("remediation", "N/A")
            )
    
    return table

def generate_console_report(results, console):
    """
    Generate and display a console report
    
    Args:
        results (dict): Scan results
        console (rich.console.Console): Console to print to
    """
    target = results.get("target", "Unknown")
    scan_time = results.get("scan_time", 0)
    vulnerabilities = results.get("vulnerabilities", {})
    summary = results.get("summary", {})
    
    # Create a summary panel
    summary_text = Text()
    summary_text.append("SCAN SUMMARY\n\n", style="bold")
    summary_text.append(f"Target: ", style="bold")
    summary_text.append(f"{target}\n")
    summary_text.append(f"Scan Duration: ", style="bold")
    summary_text.append(f"{scan_time:.2f} seconds\n")
    summary_text.append(f"Total Vulnerabilities: ", style="bold")
    summary_text.append(f"{summary.get('total_vulnerabilities', 0)}\n")
    summary_text.append(f"    Critical Risk: ", style="bold bright_red")
    summary_text.append(f"{summary.get('critical_risk', 0)}\n")
    summary_text.append(f"    High Risk: ", style="bold red")
    summary_text.append(f"{summary.get('high_risk', 0)}\n")
    summary_text.append(f"    Medium Risk: ", style="bold yellow")
    summary_text.append(f"{summary.get('medium_risk', 0)}\n")
    summary_text.append(f"    Low Risk: ", style="bold blue")
    summary_text.append(f"{summary.get('low_risk', 0)}\n")
    
    summary_panel = Panel(summary_text, title="CyberWolf Scan Report", border_style="green")
    console.print(summary_panel)
    
    # Display vulnerability tables
    if vulnerabilities.get("sql_injection"):
        console.print(generate_vulnerability_table(
            vulnerabilities.get("sql_injection", []),
            "SQL Injection Vulnerabilities",
            console
        ))
    
    if vulnerabilities.get("xss"):
        console.print(generate_vulnerability_table(
            vulnerabilities.get("xss", []),
            "Cross-Site Scripting (XSS) Vulnerabilities",
            console
        ))
    
    if vulnerabilities.get("csrf"):
        console.print(generate_vulnerability_table(
            vulnerabilities.get("csrf", []),
            "Cross-Site Request Forgery (CSRF) Vulnerabilities",
            console
        ))
    
    if vulnerabilities.get("directories"):
        console.print(generate_vulnerability_table(
            vulnerabilities.get("directories", []),
            "Directory/Path Vulnerabilities",
            console
        ))
        
    if vulnerabilities.get("tamil_brak"):
        console.print(generate_vulnerability_table(
            vulnerabilities.get("tamil_brak", []),
            "Tamil-Brak Advanced Attack Vulnerabilities",
            console
        ))
    
    # Display recommendations
    recommendations = [
        "## Security Recommendations",
        "",
        "### General Security Best Practices",
        "- Keep all software and libraries up to date",
        "- Implement proper input validation and output encoding",
        "- Use Content Security Policy (CSP) headers",
        "- Implement strong password policies",
        "- Enable HTTPS across the entire site",
        "- Use proper session management and secure cookies",
        "- Implement rate limiting and account lockout mechanisms",
        "- Regularly perform security testing and code reviews",
        "",
        "### Next Steps",
        "- Address any high-risk vulnerabilities immediately",
        "- Consider a more comprehensive security assessment",
        "- Implement a security monitoring solution",
        "- Create a security incident response plan"
    ]
    
    console.print(Panel(Markdown("\n".join(recommendations)), title="Recommendations", border_style="blue"))
